const express = require('express');
const { body, validationResult } = require('express-validator');
const { PrismaClient } = require('@prisma/client');
const { asyncHandler, AppError } = require('../middleware/errorHandler');

const router = express.Router();
const prisma = new PrismaClient();

/**
 * @swagger
 * /api/categories:
 *   get:
 *     summary: Get all categories for the user
 *     tags: [Categories]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [INCOME, EXPENSE]
 *       - in: query
 *         name: includeInactive
 *         schema:
 *           type: boolean
 *           default: false
 *     responses:
 *       200:
 *         description: Categories retrieved successfully
 */
router.get('/', asyncHandler(async (req, res) => {
  const { type, includeInactive = false } = req.query;

  const where = {
    userId: req.user.id
  };

  if (type) where.type = type;
  if (!includeInactive) where.isActive = true;

  const categories = await prisma.category.findMany({
    where,
    orderBy: [
      { sortOrder: 'asc' },
      { name: 'asc' }
    ],
    include: {
      children: {
        where: includeInactive ? {} : { isActive: true },
        orderBy: [
          { sortOrder: 'asc' },
          { name: 'asc' }
        ]
      },
      _count: {
        select: {
          transactions: true
        }
      }
    }
  });

  // Build hierarchical structure
  const rootCategories = categories.filter(cat => !cat.parentId);
  const buildHierarchy = (parentCategories) => {
    return parentCategories.map(parent => ({
      ...parent,
      children: categories.filter(cat => cat.parentId === parent.id)
    }));
  };

  const hierarchicalCategories = buildHierarchy(rootCategories);

  res.json({
    success: true,
    data: {
      categories: hierarchicalCategories,
      total: categories.length
    }
  });
}));

/**
 * @swagger
 * /api/categories:
 *   post:
 *     summary: Create a new category
 *     tags: [Categories]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - type
 *             properties:
 *               name:
 *                 type: string
 *                 minLength: 1
 *               type:
 *                 type: string
 *                 enum: [INCOME, EXPENSE]
 *               parentId:
 *                 type: string
 *               sortOrder:
 *                 type: integer
 *                 default: 0
 *     responses:
 *       201:
 *         description: Category created successfully
 */
router.post('/', [
  body('name').isLength({ min: 1 }).trim(),
  body('type').isIn(['INCOME', 'EXPENSE']),
  body('parentId').optional().isString(),
  body('sortOrder').optional().isInt({ min: 0 })
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new AppError('Validation failed', 400);
  }

  const { name, type, parentId, sortOrder = 0 } = req.body;

  // Check if category name already exists for this user and type
  const existingCategory = await prisma.category.findFirst({
    where: {
      name,
      type,
      userId: req.user.id,
      parentId: parentId || null
    }
  });

  if (existingCategory) {
    throw new AppError('Category with this name already exists', 400);
  }

  let level = 1;
  
  // If parentId is provided, verify it exists and calculate level
  if (parentId) {
    const parentCategory = await prisma.category.findFirst({
      where: {
        id: parentId,
        userId: req.user.id,
        type,
        isActive: true
      }
    });

    if (!parentCategory) {
      throw new AppError('Parent category not found or type mismatch', 404);
    }

    level = parentCategory.level + 1;

    // Limit nesting to 3 levels
    if (level > 3) {
      throw new AppError('Maximum category nesting level (3) exceeded', 400);
    }
  }

  const category = await prisma.category.create({
    data: {
      name,
      type,
      parentId,
      level,
      sortOrder,
      userId: req.user.id
    },
    include: {
      parent: {
        select: {
          id: true,
          name: true
        }
      },
      _count: {
        select: {
          transactions: true
        }
      }
    }
  });

  res.status(201).json({
    success: true,
    message: 'Category created successfully',
    data: { category }
  });
}));

/**
 * @swagger
 * /api/categories/{id}:
 *   get:
 *     summary: Get category by ID
 *     tags: [Categories]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Category retrieved successfully
 *       404:
 *         description: Category not found
 */
router.get('/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;

  const category = await prisma.category.findFirst({
    where: {
      id,
      userId: req.user.id
    },
    include: {
      parent: {
        select: {
          id: true,
          name: true,
          type: true
        }
      },
      children: {
        where: { isActive: true },
        orderBy: [
          { sortOrder: 'asc' },
          { name: 'asc' }
        ]
      },
      _count: {
        select: {
          transactions: true
        }
      }
    }
  });

  if (!category) {
    throw new AppError('Category not found', 404);
  }

  res.json({
    success: true,
    data: { category }
  });
}));

/**
 * @swagger
 * /api/categories/{id}:
 *   put:
 *     summary: Update category
 *     tags: [Categories]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 minLength: 1
 *               parentId:
 *                 type: string
 *               sortOrder:
 *                 type: integer
 *               isActive:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Category updated successfully
 *       404:
 *         description: Category not found
 */
router.put('/:id', [
  body('name').optional().isLength({ min: 1 }).trim(),
  body('parentId').optional().isString(),
  body('sortOrder').optional().isInt({ min: 0 }),
  body('isActive').optional().isBoolean()
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new AppError('Validation failed', 400);
  }

  const { id } = req.params;
  const updateData = req.body;

  // Check if category exists and belongs to user
  const existingCategory = await prisma.category.findFirst({
    where: {
      id,
      userId: req.user.id
    }
  });

  if (!existingCategory) {
    throw new AppError('Category not found', 404);
  }

  // If name is being updated, check for duplicates
  if (updateData.name && updateData.name !== existingCategory.name) {
    const duplicateCategory = await prisma.category.findFirst({
      where: {
        name: updateData.name,
        type: existingCategory.type,
        userId: req.user.id,
        parentId: updateData.parentId || existingCategory.parentId,
        id: { not: id }
      }
    });

    if (duplicateCategory) {
      throw new AppError('Category with this name already exists', 400);
    }
  }

  // If parentId is being updated, verify it and calculate new level
  if (updateData.parentId !== undefined) {
    if (updateData.parentId) {
      // Check if new parent exists and is valid
      const parentCategory = await prisma.category.findFirst({
        where: {
          id: updateData.parentId,
          userId: req.user.id,
          type: existingCategory.type,
          isActive: true
        }
      });

      if (!parentCategory) {
        throw new AppError('Parent category not found or type mismatch', 404);
      }

      // Prevent circular reference
      if (updateData.parentId === id) {
        throw new AppError('Category cannot be its own parent', 400);
      }

      updateData.level = parentCategory.level + 1;

      if (updateData.level > 3) {
        throw new AppError('Maximum category nesting level (3) exceeded', 400);
      }
    } else {
      updateData.level = 1;
    }
  }

  const category = await prisma.category.update({
    where: { id },
    data: updateData,
    include: {
      parent: {
        select: {
          id: true,
          name: true
        }
      },
      _count: {
        select: {
          transactions: true
        }
      }
    }
  });

  res.json({
    success: true,
    message: 'Category updated successfully',
    data: { category }
  });
}));

module.exports = router;
