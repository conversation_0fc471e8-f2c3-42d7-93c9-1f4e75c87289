const express = require('express');
const { query, validationResult } = require('express-validator');
const { PrismaClient } = require('@prisma/client');
const { asyncHandler, AppError } = require('../middleware/errorHandler');

const router = express.Router();
const prisma = new PrismaClient();

/**
 * @swagger
 * /api/accounts:
 *   get:
 *     summary: Get chart of accounts according to Circular 133
 *     tags: [Accounts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [ASSET, LIABILITY, EQUITY, REVENUE, EXPENSE]
 *       - in: query
 *         name: level
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 3
 *       - in: query
 *         name: includeInactive
 *         schema:
 *           type: boolean
 *           default: false
 *     responses:
 *       200:
 *         description: Chart of accounts retrieved successfully
 */
router.get('/', [
  query('type').optional().isIn(['ASSET', 'LIABILITY', 'EQUITY', 'REVENUE', 'EXPENSE']),
  query('level').optional().isInt({ min: 1, max: 3 }),
  query('includeInactive').optional().isBoolean()
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new AppError('Validation failed', 400);
  }

  const { type, level, includeInactive = false } = req.query;

  const where = {};
  if (type) where.type = type;
  if (level) where.level = parseInt(level);
  if (!includeInactive) where.isActive = true;

  const accounts = await prisma.account.findMany({
    where,
    orderBy: { code: 'asc' },
    include: {
      children: {
        where: includeInactive ? {} : { isActive: true },
        orderBy: { code: 'asc' }
      },
      _count: {
        select: {
          journalDetails: true
        }
      }
    }
  });

  // Build hierarchical structure
  const rootAccounts = accounts.filter(acc => !acc.parentCode);
  const buildHierarchy = (parentAccounts) => {
    return parentAccounts.map(parent => ({
      ...parent,
      children: accounts.filter(acc => acc.parentCode === parent.code)
    }));
  };

  const hierarchicalAccounts = buildHierarchy(rootAccounts);

  res.json({
    success: true,
    data: {
      accounts: hierarchicalAccounts,
      total: accounts.length
    }
  });
}));

/**
 * @swagger
 * /api/accounts/{code}:
 *   get:
 *     summary: Get account by code
 *     tags: [Accounts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: code
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Account retrieved successfully
 *       404:
 *         description: Account not found
 */
router.get('/:code', asyncHandler(async (req, res) => {
  const { code } = req.params;

  const account = await prisma.account.findUnique({
    where: { code },
    include: {
      parent: {
        select: {
          code: true,
          name: true,
          type: true
        }
      },
      children: {
        where: { isActive: true },
        orderBy: { code: 'asc' }
      },
      _count: {
        select: {
          journalDetails: true
        }
      }
    }
  });

  if (!account) {
    throw new AppError('Account not found', 404);
  }

  res.json({
    success: true,
    data: { account }
  });
}));

/**
 * @swagger
 * /api/accounts/balance/{code}:
 *   get:
 *     summary: Get account balance
 *     tags: [Accounts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: code
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date
 *     responses:
 *       200:
 *         description: Account balance retrieved successfully
 */
router.get('/balance/:code', [
  query('startDate').optional().isISO8601(),
  query('endDate').optional().isISO8601()
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new AppError('Validation failed', 400);
  }

  const { code } = req.params;
  const { startDate, endDate } = req.query;

  // Verify account exists
  const account = await prisma.account.findUnique({
    where: { code }
  });

  if (!account) {
    throw new AppError('Account not found', 404);
  }

  // Build date filter
  const dateFilter = {};
  if (startDate || endDate) {
    dateFilter.entry = {
      date: {}
    };
    if (startDate) dateFilter.entry.date.gte = new Date(startDate);
    if (endDate) dateFilter.entry.date.lte = new Date(endDate);
  }

  // Get journal details for this account
  const journalDetails = await prisma.journalDetail.findMany({
    where: {
      accountCode: code,
      ...dateFilter
    },
    include: {
      entry: {
        select: {
          date: true,
          description: true,
          reference: true
        }
      }
    },
    orderBy: {
      entry: {
        date: 'asc'
      }
    }
  });

  // Calculate balance
  let debitTotal = 0;
  let creditTotal = 0;

  journalDetails.forEach(detail => {
    debitTotal += parseFloat(detail.debit);
    creditTotal += parseFloat(detail.credit);
  });

  // Calculate balance based on account type
  let balance = 0;
  let balanceType = 'DEBIT';

  switch (account.type) {
    case 'ASSET':
    case 'EXPENSE':
      balance = debitTotal - creditTotal;
      balanceType = balance >= 0 ? 'DEBIT' : 'CREDIT';
      break;
    case 'LIABILITY':
    case 'EQUITY':
    case 'REVENUE':
      balance = creditTotal - debitTotal;
      balanceType = balance >= 0 ? 'CREDIT' : 'DEBIT';
      break;
  }

  res.json({
    success: true,
    data: {
      account: {
        code: account.code,
        name: account.name,
        type: account.type
      },
      balance: {
        debitTotal,
        creditTotal,
        balance: Math.abs(balance),
        balanceType,
        transactionCount: journalDetails.length
      },
      period: {
        startDate: startDate || null,
        endDate: endDate || null
      }
    }
  });
}));

/**
 * @swagger
 * /api/accounts/trial-balance:
 *   get:
 *     summary: Get trial balance report
 *     tags: [Accounts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: asOfDate
 *         schema:
 *           type: string
 *           format: date
 *       - in: query
 *         name: includeZeroBalances
 *         schema:
 *           type: boolean
 *           default: false
 *     responses:
 *       200:
 *         description: Trial balance retrieved successfully
 */
router.get('/reports/trial-balance', [
  query('asOfDate').optional().isISO8601(),
  query('includeZeroBalances').optional().isBoolean()
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new AppError('Validation failed', 400);
  }

  const { asOfDate, includeZeroBalances = false } = req.query;

  // Build date filter
  const dateFilter = {};
  if (asOfDate) {
    dateFilter.entry = {
      date: {
        lte: new Date(asOfDate)
      }
    };
  }

  // Get all accounts with their journal details
  const accounts = await prisma.account.findMany({
    where: { isActive: true },
    orderBy: { code: 'asc' },
    include: {
      journalDetails: {
        where: dateFilter,
        select: {
          debit: true,
          credit: true
        }
      }
    }
  });

  const trialBalance = [];
  let totalDebits = 0;
  let totalCredits = 0;

  accounts.forEach(account => {
    let debitTotal = 0;
    let creditTotal = 0;

    account.journalDetails.forEach(detail => {
      debitTotal += parseFloat(detail.debit);
      creditTotal += parseFloat(detail.credit);
    });

    // Calculate balance based on account type
    let debitBalance = 0;
    let creditBalance = 0;

    switch (account.type) {
      case 'ASSET':
      case 'EXPENSE':
        if (debitTotal >= creditTotal) {
          debitBalance = debitTotal - creditTotal;
        } else {
          creditBalance = creditTotal - debitTotal;
        }
        break;
      case 'LIABILITY':
      case 'EQUITY':
      case 'REVENUE':
        if (creditTotal >= debitTotal) {
          creditBalance = creditTotal - debitTotal;
        } else {
          debitBalance = debitTotal - creditTotal;
        }
        break;
    }

    // Include in trial balance if has balance or if including zero balances
    if (includeZeroBalances || debitBalance > 0 || creditBalance > 0) {
      trialBalance.push({
        code: account.code,
        name: account.name,
        type: account.type,
        debitBalance,
        creditBalance
      });

      totalDebits += debitBalance;
      totalCredits += creditBalance;
    }
  });

  res.json({
    success: true,
    data: {
      trialBalance,
      totals: {
        totalDebits,
        totalCredits,
        isBalanced: Math.abs(totalDebits - totalCredits) < 0.01
      },
      asOfDate: asOfDate || new Date().toISOString().split('T')[0],
      generatedAt: new Date().toISOString()
    }
  });
}));

module.exports = router;
