# 🔗 Hướng dẫn cấu hình Google Sheets thực tế

## 📋 Tổng quan

Hiện tại ứng dụng đang chạy ở **chế độ demo** cho t<PERSON>h năng Google Sheets. <PERSON><PERSON> sử dụng tính năng thực tế, bạn cần cấu hình Google API credentials.

## 🚀 Cách sử dụng hiện tại (Demo)

### ✅ Tính năng có sẵn:
1. **Mở file**: `client/public/standalone.html`
2. **Vào tab**: "Google Sheets" 
3. **Nhấn**: "Kết nối Google Sheets"
4. **Demo sẽ**:
   - <PERSON><PERSON> phỏng quá trình kết nối
   - Tạo cấu trúc spreadsheet ảo
   - Cho phép "đồng bộ" dữ liệu (lưu local)
   - Hiển thị trạng thái đồng bộ

### 🎯 Cách hoạt động:
- Dữ liệu vẫn lưu trên trình duyệt (localStorage)
- <PERSON><PERSON> phỏng việc đồng bộ lên Google Sheets
- Hiển thị thông báo và trạng thái như thật
- <PERSON><PERSON> thể cấu hình tự động đồng bộ

## 🔧 Cấu hình Google Sheets thực tế

### Bước 1: Tạo Google Cloud Project

1. Truy cập [Google Cloud Console](https://console.cloud.google.com/)
2. Tạo project mới hoặc chọn project có sẵn
3. Bật Google Sheets API:
   - Vào "APIs & Services" > "Library"
   - Tìm "Google Sheets API"
   - Nhấn "Enable"

### Bước 2: Tạo Credentials

1. Vào "APIs & Services" > "Credentials"
2. Nhấn "Create Credentials" > "API Key"
3. Copy API Key
4. Nhấn "Create Credentials" > "OAuth 2.0 Client ID"
5. Chọn "Web application"
6. Thêm authorized origins: `http://localhost:3000`, `file://`
7. Copy Client ID

### Bước 3: Cập nhật code

Mở file `client/public/standalone-app.js` và thay đổi:

```javascript
// Thay đổi dòng 24-28
this.googleConfig = {
    apiKey: 'YOUR_ACTUAL_API_KEY_HERE',
    clientId: 'YOUR_ACTUAL_CLIENT_ID_HERE',
    discoveryDoc: 'https://sheets.googleapis.com/$discovery/rest?version=v4',
    scopes: 'https://www.googleapis.com/auth/spreadsheets'
};
```

### Bước 4: Cập nhật phương thức kết nối

Thay thế phương thức `connectGoogleSheets()` bằng:

```javascript
async connectGoogleSheets() {
    try {
        // Initialize Google API
        await gapi.load('auth2', async () => {
            await gapi.auth2.init({
                client_id: this.googleConfig.clientId,
                scope: this.googleConfig.scopes
            });
            
            const authInstance = gapi.auth2.getAuthInstance();
            const user = await authInstance.signIn();
            
            if (user.isSignedIn()) {
                // Create new spreadsheet
                const response = await gapi.client.sheets.spreadsheets.create({
                    properties: {
                        title: this.syncSettings.sheetName
                    },
                    sheets: [
                        { properties: { title: 'Thu nhập' } },
                        { properties: { title: 'Chi phí' } },
                        { properties: { title: 'Tổng hợp' } }
                    ]
                });
                
                this.spreadsheetId = response.result.spreadsheetId;
                localStorage.setItem('spreadsheetId', this.spreadsheetId);
                
                await this.setupSheetHeaders();
                this.updateGoogleSheetsUI();
                
                alert('✅ Đã kết nối thành công với Google Sheets!');
            }
        });
    } catch (error) {
        console.error('Error:', error);
        alert('❌ Lỗi kết nối: ' + error.message);
    }
}
```

## 📊 Cấu trúc Google Sheet sẽ được tạo

### Sheet "Thu nhập":
| Ngày | Mô tả | Danh mục | Số tiền | Ghi chú | Tham chiếu |
|------|-------|----------|---------|---------|------------|
| 15/01/2024 | Doanh thu tư vấn | Dịch vụ tư vấn | 15,000,000 | Khách hàng A | REF001 |

### Sheet "Chi phí":
| Ngày | Mô tả | Danh mục | Số tiền | Ghi chú | Tham chiếu |
|------|-------|----------|---------|---------|------------|
| 14/01/2024 | Văn phòng phẩm | Chi phí văn phòng | 2,500,000 | Mua máy in | REF002 |

### Sheet "Tổng hợp":
| Tháng | Tổng thu | Tổng chi | Lợi nhuận | Số giao dịch |
|-------|----------|----------|-----------|--------------|
| 01/2024 | 50,000,000 | 30,000,000 | 20,000,000 | 45 |

## 🔄 Tính năng đồng bộ

### Tự động đồng bộ:
- **Ngay lập tức**: Mỗi khi thêm giao dịch mới
- **Hàng ngày**: Vào 23:59 mỗi ngày
- **Thủ công**: Chỉ khi nhấn nút "Đồng bộ"

### Dữ liệu được đồng bộ:
- ✅ Tất cả giao dịch thu/chi
- ✅ Báo cáo tổng hợp theo tháng
- ✅ Thống kê và phân tích
- ✅ Metadata (ngày tạo, cập nhật)

## 🔒 Bảo mật và quyền riêng tư

### ✅ An toàn:
- Chỉ bạn có quyền truy cập Google Sheet
- Dữ liệu được mã hóa khi truyền tải
- Không lưu trữ mật khẩu Google
- Có thể thu hồi quyền truy cập bất cứ lúc nào

### 🛡️ Quyền được yêu cầu:
- **Google Sheets**: Tạo, đọc, ghi spreadsheet
- **Google Drive**: Tạo file mới (cho spreadsheet)

## 🚨 Lưu ý quan trọng

### ⚠️ Giới hạn API:
- Google Sheets API có giới hạn 100 requests/100 seconds/user
- Ứng dụng đã tối ưu để không vượt quá giới hạn
- Nếu có nhiều giao dịch, đồng bộ sẽ được chia nhỏ

### 💡 Khuyến nghị:
1. **Backup định kỳ**: Xuất dữ liệu ra file Excel/CSV
2. **Kiểm tra đồng bộ**: Thỉnh thoảng kiểm tra Google Sheet
3. **Bảo mật tài khoản**: Bật 2FA cho Google Account
4. **Chia sẻ cẩn thận**: Không chia sẻ link Google Sheet

## 🆘 Khắc phục sự cố

### ❓ Lỗi thường gặp:

**"API key not valid"**
```
- Kiểm tra API key đã đúng chưa
- Đảm bảo đã bật Google Sheets API
- Kiểm tra domain restrictions
```

**"Access denied"**
```
- Kiểm tra OAuth consent screen
- Đảm bảo email được thêm vào test users
- Kiểm tra scopes được cấp quyền
```

**"Quota exceeded"**
```
- Đợi 1 phút rồi thử lại
- Giảm tần suất đồng bộ tự động
- Kiểm tra usage trong Google Cloud Console
```

## 📞 Hỗ trợ

Nếu cần hỗ trợ cấu hình Google Sheets:
- 📧 Email: <EMAIL>
- 📖 Docs: [Google Sheets API Documentation](https://developers.google.com/sheets/api)
- 🎥 Video hướng dẫn: [Sẽ cập nhật]

---

**💡 Tip**: Hiện tại bạn có thể sử dụng chế độ demo để trải nghiệm đầy đủ tính năng mà không cần cấu hình phức tạp!
