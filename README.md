# Ứng dụng Quản lý Kế toán theo Thông tư 133/2016/TT-BTC

## Tổng quan

Ứng dụng web quản lý kế toán đơn gi<PERSON>, thân thiện với người dùng, tuân thủ Thông tư 133/2016/TT-BTC của Bộ Tài chính Việt Nam.

## Tính năng chính

### Giai đoạn 1: Nền tảng cơ bản
- ✅ Dashboard tổng quan dòng tiền
- ✅ Form nhập thu/chi với wizard thông minh
- ✅ Quản lý danh mục giao dịch
- ✅ Tích hợp Google Sheets
- ✅ Hệ thống cảnh báo và automation

### Giai đoạn 2: Nghi<PERSON><PERSON> vụ kế toán
- 🔄 Hệ thống tài khoản kế toán theo TT 133
- 🔄 Tự động tạo bút toán
- 🔄 Quản lý thuế GTGT, TNDN, TNCN
- 🔄 Validation và kiểm tra cân đối

### Giai đoạn 3: <PERSON><PERSON><PERSON> cáo và thống kê
- ⏳ Báo cáo thuế tự động
- ⏳ Báo cáo tài chính cơ bản
- ⏳ Dashboard analytics
- ⏳ Kết chuyển và khóa sổ

## Công nghệ sử dụng

### Frontend
- **React.js** - UI framework
- **Material-UI** - Component library
- **Chart.js** - Biểu đồ và thống kê
- **React Router** - Navigation
- **Axios** - HTTP client

### Backend
- **Node.js** - Runtime environment
- **Express.js** - Web framework
- **PostgreSQL** - Database
- **Prisma** - ORM
- **JWT** - Authentication

### Tích hợp
- **Google Sheets API** - Đồng bộ dữ liệu
- **Google OAuth2** - Authentication
- **Nodemailer** - Email notifications

## Cài đặt và chạy

### Yêu cầu hệ thống
- Node.js >= 16.0.0
- PostgreSQL >= 13.0
- npm hoặc yarn

### Cài đặt
```bash
# Clone repository
git clone <repository-url>
cd app_ke_toan_133

# Cài đặt dependencies cho tất cả modules
npm run install:all

# Thiết lập database
cd server
cp .env.example .env
# Cập nhật thông tin database trong .env
npm run db:migrate
npm run db:seed

# Chạy ứng dụng
cd ..
npm run dev
```

### Truy cập ứng dụng
- Frontend: http://localhost:3000
- Backend API: http://localhost:5000
- API Documentation: http://localhost:5000/api-docs

## Cấu trúc thư mục

```
app_ke_toan_133/
├── client/                 # React frontend
│   ├── src/
│   │   ├── components/     # Reusable components
│   │   ├── pages/         # Page components
│   │   ├── hooks/         # Custom hooks
│   │   ├── services/      # API services
│   │   ├── utils/         # Utility functions
│   │   └── styles/        # CSS/SCSS files
│   └── public/
├── server/                # Node.js backend
│   ├── src/
│   │   ├── controllers/   # Route controllers
│   │   ├── models/        # Database models
│   │   ├── routes/        # API routes
│   │   ├── middleware/    # Express middleware
│   │   ├── services/      # Business logic
│   │   └── utils/         # Utility functions
│   └── prisma/           # Database schema & migrations
├── docs/                 # Documentation
└── shared/              # Shared types & utilities
```

## API Documentation

Xem chi tiết tại: [API Documentation](./docs/api.md)

## Đóng góp

1. Fork repository
2. Tạo feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Tạo Pull Request

## License

MIT License - xem [LICENSE](./LICENSE) để biết thêm chi tiết.

## Liên hệ

- Email: <EMAIL>
- Documentation: [Wiki](./docs/)
- Issues: [GitHub Issues](./issues)
