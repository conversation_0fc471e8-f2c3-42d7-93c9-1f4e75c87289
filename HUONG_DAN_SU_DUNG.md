# 🚀 HƯỚNG DẪN SỬ DỤNG APP KẾ TOÁN TT 133

## 📋 Có 2 cách sử dụng ứng dụng:

### 🌐 **CÁCH 1: CHẠY TRỰC TIẾP + GOOGLE SHEETS (Khuyến nghị cho người dùng cuối)**

**✅ Ưu điểm:**
- Không cần cài đặt gì
- Ch<PERSON>y ngay trên trình duyệt
- <PERSON><PERSON> sử dụng, giao diện đẹp
- **🆕 Tích hợp Google Sheets**: Đồng bộ dữ liệu lên cloud
- **🆕 Truy cập mọi nơi**: Xem báo cáo trên Google Sheets
- **🆕 Tự động backup**: Dữ liệu an toàn trên Google Drive

**📱 Cách sử dụng:**
1. Mở file `client/public/standalone.html` bằng trình duyệt
2. Thêm giao dịch như bình thường
3. **Vào tab "Google Sheets"** để kết nối và đồng bộ
4. <PERSON><PERSON>t đầu sử dụng ngay!

**🎯 <PERSON><PERSON> hợp với:**
- <PERSON><PERSON><PERSON> nghiệp nhỏ, hộ kinh doanh
- Người dùng không am hiểu kỹ thuật
- Cần truy cập dữ liệu từ nhiều thiết bị
- Muốn backup tự động lên cloud

---

### 💻 **CÁCH 2: CÀI ĐẶT ĐẦY ĐỦ (Cho developer và doanh nghiệp lớn)**

**✅ Ưu điểm:**
- Hiệu năng cao, bảo mật tốt
- Có thể tùy biến và phát triển thêm
- Hỗ trợ nhiều người dùng
- Tích hợp Google Sheets
- Báo cáo chi tiết theo TT 133

**⚙️ Yêu cầu hệ thống:**
- Node.js >= 16.0
- PostgreSQL >= 13.0
- 2GB RAM, 1GB ổ cứng

**📥 Cách cài đặt:**

#### Windows:
```cmd
# Bước 1: Chạy script tự động
install.bat

# Bước 2: Tạo database
createdb app_ke_toan_133

# Bước 3: Chạy migrations
cd server
npm run db:migrate
npm run db:seed

# Bước 4: Khởi động ứng dụng
cd ..
npm run dev
```

#### Linux/Mac:
```bash
# Bước 1: Chạy script tự động
chmod +x install.sh
./install.sh

# Bước 2: Tạo database
createdb app_ke_toan_133

# Bước 3: Chạy migrations
cd server
npm run db:migrate
npm run db:seed

# Bước 4: Khởi động ứng dụng
cd ..
npm run dev
```

**🌐 Truy cập:**
- Frontend: http://localhost:3000
- Backend API: http://localhost:5000
- API Docs: http://localhost:5000/api-docs

---

## 📊 TÍNH NĂNG CHÍNH

### 🏠 **Dashboard**
- Tổng quan tài chính tháng hiện tại
- Biểu đồ thu chi
- Giao dịch gần đây
- Cảnh báo nghĩa vụ thuế

### 💰 **Quản lý giao dịch**
- Thêm/sửa/xóa giao dịch
- Phân loại thu/chi theo danh mục
- Tìm kiếm và lọc giao dịch
- Ghi chú chi tiết

### 📈 **Báo cáo tài chính**
- Báo cáo thu chi theo tháng/quý/năm
- Báo cáo lưu chuyển tiền tệ
- Phân tích theo danh mục
- Xuất file Excel/PDF

### 🏛️ **Quản lý thuế**
- Tính thuế GTGT (0%, 5%, 10%)
- Tính thuế TNDN (10%, 20%)
- Tính thuế TNCN lũy tiến
- Nhắc nhở hạn nộp thuế
- Tạo tờ khai thuế

### ⚙️ **Tính năng khác**
- **🆕 Đồng bộ Google Sheets**: Tự động hoặc thủ công
- **🆕 Truy cập cloud**: Xem dữ liệu trên Google Sheets
- **🆕 Backup tự động**: An toàn với Google Drive
- Cài đặt cá nhân hóa
- Hỗ trợ đa ngôn ngữ

### 🔗 **Google Sheets Integration**
- **Kết nối dễ dàng**: 1 click để kết nối Google account
- **Tự động tạo spreadsheet**: 3 sheet (Thu nhập, Chi phí, Tổng hợp)
- **Đồng bộ thông minh**: Chọn tự động hoặc thủ công
- **Truy cập mọi nơi**: Xem báo cáo trên điện thoại, máy tính
- **Chia sẻ an toàn**: Chia sẻ báo cáo với kế toán, đối tác

---

## 🎯 SO SÁNH HAI PHƯƠNG ÁN

| Tính năng | Standalone + Sheets | Cài đặt đầy đủ |
|-----------|---------------------|----------------|
| **Dễ sử dụng** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |
| **Hiệu năng** | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| **Bảo mật** | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| **Tính năng** | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| **Tùy biến** | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| **Nhiều user** | ❌ | ✅ |
| **Google Sheets** | ✅ **Mới!** | ✅ |
| **Báo cáo TT 133** | Đầy đủ | Đầy đủ |
| **Truy cập cloud** | ✅ **Mới!** | ✅ |
| **Auto backup** | ✅ **Mới!** | ✅ |

---

## 🆘 HỖ TRỢ VÀ KHẮC PHỤC SỰ CỐ

### ❓ **Câu hỏi thường gặp**

**Q: Dữ liệu có bị mất không?**
- Standalone: Lưu trên trình duyệt, có thể mất khi xóa cache
- Cài đặt đầy đủ: Lưu trên database, an toàn hơn

**Q: Có thể sử dụng offline không?**
- Standalone: Có, sau khi tải trang lần đầu
- Cài đặt đầy đủ: Có, khi chạy local

**Q: Có tuân thủ quy định Việt Nam không?**
- Cả hai phiên bản đều tuân thủ TT 133/2016/TT-BTC
- Tính thuế theo quy định hiện hành

### 🔧 **Khắc phục sự cố**

**Lỗi không mở được standalone:**
```
1. Đảm bảo trình duyệt hỗ trợ JavaScript
2. Thử trình duyệt khác (Chrome, Firefox, Edge)
3. Kiểm tra file standalone.html có bị lỗi không
```

**Lỗi cài đặt đầy đủ:**
```
1. Kiểm tra Node.js: node --version
2. Kiểm tra PostgreSQL: psql --version
3. Xem log lỗi trong terminal
4. Đọc file setup.md để biết chi tiết
```

### 📞 **Liên hệ hỗ trợ**
- Email: <EMAIL>
- GitHub Issues: [Link repository]
- Documentation: Xem thư mục `docs/`

---

## 🎉 **BẮT ĐẦU NGAY**

### 🌟 Cho người dùng cuối (Khuyến nghị):
👉 **Mở file `client/public/standalone.html`** và bắt đầu sử dụng!

**🔗 Để kết nối Google Sheets:**
1. Vào tab "Google Sheets" trong ứng dụng
2. Nhấn "Kết nối Google Sheets"
3. Làm theo hướng dẫn (hiện tại là demo mode)
4. Xem file `GOOGLE_SHEETS_SETUP.md` để cấu hình thực tế

### 💻 Cho developer:
👉 **Chạy `./install.sh`** và làm theo hướng dẫn trong `setup.md`

---

*© 2024 App Kế toán TT 133 - Ứng dụng kế toán tuân thủ pháp luật Việt Nam*
