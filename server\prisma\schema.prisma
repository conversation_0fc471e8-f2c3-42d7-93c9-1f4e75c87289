// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                String   @id @default(cuid())
  email             String   @unique
  password          String?
  name              String?
  avatar            String?
  googleId          String?  @unique
  googleSheetId     String?
  settings          Json?
  isActive          Boolean  @default(true)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relations
  transactions      Transaction[]
  categories        Category[]
  journalEntries    JournalEntry[]
  taxObligations    TaxObligation[]
  taxDeclarations   TaxDeclaration[]

  @@map("users")
}

model Category {
  id          String   @id @default(cuid())
  name        String
  type        CategoryType
  parentId    String?
  level       Int      @default(1)
  isActive    Boolean  @default(true)
  sortOrder   Int      @default(0)
  userId      String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  user         User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  parent       Category?     @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children     Category[]    @relation("CategoryHierarchy")
  transactions Transaction[]

  @@map("categories")
}

model Transaction {
  id              String      @id @default(cuid())
  type            TransactionType
  amount          Decimal     @db.Decimal(15, 2)
  description     String
  notes           String?
  date            DateTime
  formulaDetails  Json?       // Stores calculation details for tax/accounting
  reference       String?     // Reference number
  isReconciled    Boolean     @default(false)
  userId          String
  categoryId      String
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt

  // Relations
  user            User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  category        Category    @relation(fields: [categoryId], references: [id])
  journalDetails  JournalDetail[]

  @@map("transactions")
}

model Account {
  id          String      @id @default(cuid())
  code        String      @unique
  name        String
  type        AccountType
  parentCode  String?
  level       Int         @default(1)
  isActive    Boolean     @default(true)
  description String?
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  parent         Account?        @relation("AccountHierarchy", fields: [parentCode], references: [code])
  children       Account[]       @relation("AccountHierarchy")
  journalDetails JournalDetail[]

  @@map("accounts")
}

model JournalEntry {
  id          String   @id @default(cuid())
  date        DateTime
  description String
  reference   String?
  userId      String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  user    User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  details JournalDetail[]

  @@map("journal_entries")
}

model JournalDetail {
  id            String  @id @default(cuid())
  entryId       String
  accountCode   String
  debit         Decimal @default(0) @db.Decimal(15, 2)
  credit        Decimal @default(0) @db.Decimal(15, 2)
  description   String?
  transactionId String?

  // Relations
  entry       JournalEntry @relation(fields: [entryId], references: [id], onDelete: Cascade)
  account     Account      @relation(fields: [accountCode], references: [code])
  transaction Transaction? @relation(fields: [transactionId], references: [id])

  @@map("journal_details")
}

model TaxConfig {
  id                 String   @id @default(cuid())
  taxType            TaxType
  rate               Decimal  @db.Decimal(5, 4)
  description        String
  applicableAccounts String[] // Array of account codes
  isActive           Boolean  @default(true)
  effectiveFrom      DateTime
  effectiveTo        DateTime?
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt

  @@map("tax_config")
}

model TaxObligation {
  id         String           @id @default(cuid())
  period     String           // Format: YYYY-MM or YYYY-QQ
  taxType    TaxType
  baseAmount Decimal          @db.Decimal(15, 2)
  taxAmount  Decimal          @db.Decimal(15, 2)
  dueDate    DateTime
  status     TaxObligationStatus @default(PENDING)
  userId     String
  createdAt  DateTime         @default(now())
  updatedAt  DateTime         @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("tax_obligations")
}

model TaxDeclaration {
  id              String   @id @default(cuid())
  period          String   // Format: YYYY-MM or YYYY-QQ
  taxType         TaxType
  declarationData Json     // Stores the complete declaration form data
  submittedDate   DateTime?
  userId          String
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("tax_declarations")
}

// Enums
enum CategoryType {
  INCOME
  EXPENSE
}

enum TransactionType {
  INCOME
  EXPENSE
}

enum AccountType {
  ASSET
  LIABILITY
  EQUITY
  REVENUE
  EXPENSE
}

enum TaxType {
  VAT        // Thuế GTGT
  CIT        // Thuế TNDN
  PIT        // Thuế TNCN
  LICENSE    // Thuế môn bài
  RESOURCE   // Thuế tài nguyên
  ENVIRONMENTAL // Phí bảo vệ môi trường
  OTHER
}

enum TaxObligationStatus {
  PENDING
  PAID
  OVERDUE
  WAIVED
}
