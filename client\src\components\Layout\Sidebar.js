import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  Box,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Divider,
  Typography,
  Toolbar,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  AccountBalance as AccountBalanceIcon,
  Category as CategoryIcon,
  Assessment as AssessmentIcon,
  Receipt as ReceiptIcon,
  Settings as SettingsIcon,
  Add as AddIcon,
  TrendingUp as TrendingUpIcon,
} from '@mui/icons-material';

const menuItems = [
  {
    title: 'Tổng quan',
    path: '/dashboard',
    icon: <DashboardIcon />,
  },
  {
    title: 'Giao dịch',
    items: [
      {
        title: 'Danh sách giao dịch',
        path: '/transactions',
        icon: <ReceiptIcon />,
      },
      {
        title: 'Thêm giao dịch',
        path: '/transactions/new',
        icon: <AddIcon />,
      },
    ],
  },
  {
    title: '<PERSON>h mục',
    path: '/categories',
    icon: <CategoryIcon />,
  },
  {
    title: '<PERSON><PERSON>o cáo',
    path: '/reports',
    icon: <AssessmentIcon />,
  },
  {
    title: 'Quản lý thuế',
    path: '/tax',
    icon: <AccountBalanceIcon />,
  },
  {
    title: 'Cài đặt',
    path: '/settings',
    icon: <SettingsIcon />,
  },
];

const Sidebar = ({ onMobileClose }) => {
  const location = useLocation();
  const navigate = useNavigate();

  const handleNavigation = (path) => {
    navigate(path);
    if (onMobileClose) {
      onMobileClose();
    }
  };

  const isActive = (path) => {
    return location.pathname === path || location.pathname.startsWith(path + '/');
  };

  const renderMenuItem = (item, isSubItem = false) => {
    const active = isActive(item.path);
    
    return (
      <ListItem key={item.path} disablePadding>
        <ListItemButton
          onClick={() => handleNavigation(item.path)}
          sx={{
            pl: isSubItem ? 4 : 2,
            pr: 2,
            py: 1.5,
            borderRadius: 1,
            mx: 1,
            mb: 0.5,
            backgroundColor: active ? 'primary.main' : 'transparent',
            color: active ? 'primary.contrastText' : 'text.primary',
            '&:hover': {
              backgroundColor: active ? 'primary.dark' : 'action.hover',
            },
          }}
        >
          <ListItemIcon
            sx={{
              color: active ? 'primary.contrastText' : 'text.secondary',
              minWidth: 40,
            }}
          >
            {item.icon}
          </ListItemIcon>
          <ListItemText
            primary={item.title}
            primaryTypographyProps={{
              fontSize: '0.875rem',
              fontWeight: active ? 600 : 500,
            }}
          />
        </ListItemButton>
      </ListItem>
    );
  };

  const renderMenuSection = (section) => {
    if (section.path) {
      // Single menu item
      return renderMenuItem(section);
    }

    // Menu section with sub-items
    return (
      <Box key={section.title}>
        <ListItem>
          <Typography
            variant="overline"
            sx={{
              fontSize: '0.75rem',
              fontWeight: 600,
              color: 'text.secondary',
              letterSpacing: 1,
              px: 1,
            }}
          >
            {section.title}
          </Typography>
        </ListItem>
        {section.items.map((item) => renderMenuItem(item, true))}
      </Box>
    );
  };

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Logo/Brand area */}
      <Toolbar
        sx={{
          px: 2,
          py: 3,
          borderBottom: '1px solid',
          borderBottomColor: 'divider',
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Box
            sx={{
              width: 40,
              height: 40,
              borderRadius: 2,
              backgroundColor: 'primary.main',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <TrendingUpIcon sx={{ color: 'white', fontSize: 24 }} />
          </Box>
          <Box>
            <Typography variant="h6" sx={{ fontWeight: 700, lineHeight: 1.2 }}>
              Kế toán
            </Typography>
            <Typography variant="caption" color="text.secondary">
              TT 133/2016
            </Typography>
          </Box>
        </Box>
      </Toolbar>

      {/* Navigation menu */}
      <Box sx={{ flex: 1, overflow: 'auto', py: 2 }}>
        <List disablePadding>
          {menuItems.map((item, index) => (
            <React.Fragment key={item.title || index}>
              {renderMenuSection(item)}
              {index < menuItems.length - 1 && item.items && (
                <Divider sx={{ my: 2, mx: 2 }} />
              )}
            </React.Fragment>
          ))}
        </List>
      </Box>

      {/* Footer */}
      <Box
        sx={{
          p: 2,
          borderTop: '1px solid',
          borderTopColor: 'divider',
          backgroundColor: 'background.paper',
        }}
      >
        <Typography variant="caption" color="text.secondary" align="center">
          © 2024 App Kế toán TT 133
        </Typography>
      </Box>
    </Box>
  );
};

export default Sidebar;
