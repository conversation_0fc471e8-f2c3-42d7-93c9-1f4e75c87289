{"name": "app-ke-toan-133", "version": "1.0.0", "description": "Ứng dụng quản lý kế toán theo Thông tư 133/2016/TT-BTC", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run server:dev\" \"npm run client:dev\"", "server:dev": "cd server && npm run dev", "client:dev": "cd client && npm start", "build": "cd client && npm run build", "start": "cd server && npm start", "test": "npm run test:client && npm run test:server", "test:client": "cd client && npm test", "test:server": "cd server && npm test", "install:all": "npm install && cd client && npm install && cd ../server && npm install"}, "keywords": ["accounting", "vietnam", "thong-tu-133", "tax", "financial-management"], "author": "Accounting App Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "workspaces": ["client", "server"]}