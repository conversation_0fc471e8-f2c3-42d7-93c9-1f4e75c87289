import axios from 'axios';

// Create axios instance
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || '/api',
  timeout: 10000,
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: (credentials) => api.post('/auth/login', credentials),
  register: (userData) => api.post('/auth/register', userData),
  getCurrentUser: () => api.get('/auth/me'),
  updateProfile: (profileData) => api.put('/auth/update-profile', profileData),
  changePassword: (passwordData) => api.put('/auth/change-password', passwordData),
};

// Transactions API
export const transactionsAPI = {
  getTransactions: (params) => api.get('/transactions', { params }),
  getTransaction: (id) => api.get(`/transactions/${id}`),
  createTransaction: (data) => api.post('/transactions', data),
  updateTransaction: (id, data) => api.put(`/transactions/${id}`, data),
  deleteTransaction: (id) => api.delete(`/transactions/${id}`),
  getDashboardStats: (params) => api.get('/transactions/dashboard/stats', { params }),
};

// Categories API
export const categoriesAPI = {
  getCategories: (params) => api.get('/categories', { params }),
  getCategory: (id) => api.get(`/categories/${id}`),
  createCategory: (data) => api.post('/categories', data),
  updateCategory: (id, data) => api.put(`/categories/${id}`, data),
  deleteCategory: (id) => api.delete(`/categories/${id}`),
};

// Accounts API
export const accountsAPI = {
  getAccounts: (params) => api.get('/accounts', { params }),
  getAccount: (code) => api.get(`/accounts/${code}`),
  getAccountBalance: (code, params) => api.get(`/accounts/balance/${code}`, { params }),
  getTrialBalance: (params) => api.get('/accounts/reports/trial-balance', { params }),
};

// Tax API
export const taxAPI = {
  getTaxConfig: (params) => api.get('/tax/config', { params }),
  calculateTax: (data) => api.post('/tax/calculate', data),
  getTaxObligations: (params) => api.get('/tax/obligations', { params }),
  createTaxObligation: (data) => api.post('/tax/obligations', data),
  updateTaxObligation: (id, data) => api.put(`/tax/obligations/${id}`, data),
  deleteTaxObligation: (id) => api.delete(`/tax/obligations/${id}`),
};

// Reports API
export const reportsAPI = {
  getIncomeStatement: (params) => api.get('/reports/income-statement', { params }),
  getCashFlow: (params) => api.get('/reports/cash-flow', { params }),
  getTaxSummary: (params) => api.get('/reports/tax-summary', { params }),
  getDashboard: (params) => api.get('/reports/dashboard', { params }),
};

// Google Sheets API
export const googleSheetsAPI = {
  getAuthUrl: () => api.get('/google-sheets/auth-url'),
  handleCallback: (data) => api.post('/google-sheets/callback', data),
  syncData: (data) => api.post('/google-sheets/sync', data),
  disconnect: () => api.post('/google-sheets/disconnect'),
};

// Utility functions
export const formatCurrency = (amount, currency = 'VND') => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};

export const formatNumber = (number) => {
  return new Intl.NumberFormat('vi-VN').format(number);
};

export const formatDate = (date, format = 'DD/MM/YYYY') => {
  if (!date) return '';
  
  const d = new Date(date);
  const day = String(d.getDate()).padStart(2, '0');
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const year = d.getFullYear();
  
  switch (format) {
    case 'DD/MM/YYYY':
      return `${day}/${month}/${year}`;
    case 'MM/DD/YYYY':
      return `${month}/${day}/${year}`;
    case 'YYYY-MM-DD':
      return `${year}-${month}-${day}`;
    default:
      return `${day}/${month}/${year}`;
  }
};

export const parseAmount = (amountString) => {
  if (!amountString) return 0;
  // Remove currency symbols and spaces, replace comma with dot
  return parseFloat(amountString.toString().replace(/[^\d.-]/g, '')) || 0;
};

export const validateEmail = (email) => {
  const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return re.test(email);
};

export const validatePhone = (phone) => {
  const re = /^[0-9]{10,11}$/;
  return re.test(phone.replace(/\s/g, ''));
};

// Export default api instance
export default api;
