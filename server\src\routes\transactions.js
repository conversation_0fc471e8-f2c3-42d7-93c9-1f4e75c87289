const express = require('express');
const { body, query, validationResult } = require('express-validator');
const { PrismaClient } = require('@prisma/client');
const { asyncHandler, AppError } = require('../middleware/errorHandler');

const router = express.Router();
const prisma = new PrismaClient();

/**
 * @swagger
 * /api/transactions:
 *   get:
 *     summary: Get transactions with filtering and pagination
 *     tags: [Transactions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [INCOME, EXPENSE]
 *       - in: query
 *         name: categoryId
 *         schema:
 *           type: string
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Transactions retrieved successfully
 */
router.get('/', [
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('type').optional().isIn(['INCOME', 'EXPENSE']),
  query('startDate').optional().isISO8601(),
  query('endDate').optional().isISO8601()
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new AppError('Validation failed', 400);
  }

  const {
    page = 1,
    limit = 20,
    type,
    categoryId,
    startDate,
    endDate,
    search
  } = req.query;

  const skip = (parseInt(page) - 1) * parseInt(limit);

  // Build where clause
  const where = {
    userId: req.user.id
  };

  if (type) where.type = type;
  if (categoryId) where.categoryId = categoryId;

  if (startDate || endDate) {
    where.date = {};
    if (startDate) where.date.gte = new Date(startDate);
    if (endDate) where.date.lte = new Date(endDate);
  }

  if (search) {
    where.OR = [
      { description: { contains: search, mode: 'insensitive' } },
      { notes: { contains: search, mode: 'insensitive' } },
      { reference: { contains: search, mode: 'insensitive' } }
    ];
  }

  // Get transactions with pagination
  const [transactions, total] = await Promise.all([
    prisma.transaction.findMany({
      where,
      include: {
        category: {
          select: {
            id: true,
            name: true,
            type: true
          }
        }
      },
      orderBy: { date: 'desc' },
      skip,
      take: parseInt(limit)
    }),
    prisma.transaction.count({ where })
  ]);

  // Calculate summary statistics
  const summary = await prisma.transaction.aggregate({
    where: {
      userId: req.user.id,
      ...(startDate || endDate ? {
        date: {
          ...(startDate && { gte: new Date(startDate) }),
          ...(endDate && { lte: new Date(endDate) })
        }
      } : {})
    },
    _sum: {
      amount: true
    },
    _count: true
  });

  const incomeSum = await prisma.transaction.aggregate({
    where: {
      userId: req.user.id,
      type: 'INCOME',
      ...(startDate || endDate ? {
        date: {
          ...(startDate && { gte: new Date(startDate) }),
          ...(endDate && { lte: new Date(endDate) })
        }
      } : {})
    },
    _sum: { amount: true }
  });

  const expenseSum = await prisma.transaction.aggregate({
    where: {
      userId: req.user.id,
      type: 'EXPENSE',
      ...(startDate || endDate ? {
        date: {
          ...(startDate && { gte: new Date(startDate) }),
          ...(endDate && { lte: new Date(endDate) })
        }
      } : {})
    },
    _sum: { amount: true }
  });

  res.json({
    success: true,
    data: {
      transactions,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      },
      summary: {
        totalTransactions: summary._count,
        totalAmount: summary._sum.amount || 0,
        totalIncome: incomeSum._sum.amount || 0,
        totalExpense: expenseSum._sum.amount || 0,
        netAmount: (incomeSum._sum.amount || 0) - (expenseSum._sum.amount || 0)
      }
    }
  });
}));

/**
 * @swagger
 * /api/transactions:
 *   post:
 *     summary: Create a new transaction
 *     tags: [Transactions]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - type
 *               - amount
 *               - description
 *               - categoryId
 *               - date
 *             properties:
 *               type:
 *                 type: string
 *                 enum: [INCOME, EXPENSE]
 *               amount:
 *                 type: number
 *                 minimum: 0.01
 *               description:
 *                 type: string
 *                 minLength: 1
 *               categoryId:
 *                 type: string
 *               date:
 *                 type: string
 *                 format: date-time
 *               notes:
 *                 type: string
 *               reference:
 *                 type: string
 *               formulaDetails:
 *                 type: object
 *     responses:
 *       201:
 *         description: Transaction created successfully
 */
router.post('/', [
  body('type').isIn(['INCOME', 'EXPENSE']),
  body('amount').isFloat({ min: 0.01 }),
  body('description').isLength({ min: 1 }).trim(),
  body('categoryId').isString().notEmpty(),
  body('date').isISO8601(),
  body('notes').optional().trim(),
  body('reference').optional().trim(),
  body('formulaDetails').optional().isObject()
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new AppError('Validation failed', 400);
  }

  const {
    type,
    amount,
    description,
    categoryId,
    date,
    notes,
    reference,
    formulaDetails
  } = req.body;

  // Verify category belongs to user
  const category = await prisma.category.findFirst({
    where: {
      id: categoryId,
      userId: req.user.id,
      isActive: true
    }
  });

  if (!category) {
    throw new AppError('Category not found or not accessible', 404);
  }

  // Verify category type matches transaction type
  if (category.type !== type) {
    throw new AppError('Category type does not match transaction type', 400);
  }

  const transaction = await prisma.transaction.create({
    data: {
      type,
      amount,
      description,
      categoryId,
      date: new Date(date),
      notes,
      reference,
      formulaDetails,
      userId: req.user.id
    },
    include: {
      category: {
        select: {
          id: true,
          name: true,
          type: true
        }
      }
    }
  });

  res.status(201).json({
    success: true,
    message: 'Transaction created successfully',
    data: { transaction }
  });
}));

/**
 * @swagger
 * /api/transactions/{id}:
 *   get:
 *     summary: Get transaction by ID
 *     tags: [Transactions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Transaction retrieved successfully
 *       404:
 *         description: Transaction not found
 */
router.get('/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;

  const transaction = await prisma.transaction.findFirst({
    where: {
      id,
      userId: req.user.id
    },
    include: {
      category: {
        select: {
          id: true,
          name: true,
          type: true
        }
      },
      journalDetails: {
        include: {
          account: {
            select: {
              code: true,
              name: true
            }
          }
        }
      }
    }
  });

  if (!transaction) {
    throw new AppError('Transaction not found', 404);
  }

  res.json({
    success: true,
    data: { transaction }
  });
}));

/**
 * @swagger
 * /api/transactions/{id}:
 *   put:
 *     summary: Update transaction
 *     tags: [Transactions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               type:
 *                 type: string
 *                 enum: [INCOME, EXPENSE]
 *               amount:
 *                 type: number
 *                 minimum: 0.01
 *               description:
 *                 type: string
 *                 minLength: 1
 *               categoryId:
 *                 type: string
 *               date:
 *                 type: string
 *                 format: date-time
 *               notes:
 *                 type: string
 *               reference:
 *                 type: string
 *               formulaDetails:
 *                 type: object
 *     responses:
 *       200:
 *         description: Transaction updated successfully
 *       404:
 *         description: Transaction not found
 */
router.put('/:id', [
  body('type').optional().isIn(['INCOME', 'EXPENSE']),
  body('amount').optional().isFloat({ min: 0.01 }),
  body('description').optional().isLength({ min: 1 }).trim(),
  body('categoryId').optional().isString().notEmpty(),
  body('date').optional().isISO8601(),
  body('notes').optional().trim(),
  body('reference').optional().trim(),
  body('formulaDetails').optional().isObject()
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new AppError('Validation failed', 400);
  }

  const { id } = req.params;
  const updateData = req.body;

  // Check if transaction exists and belongs to user
  const existingTransaction = await prisma.transaction.findFirst({
    where: {
      id,
      userId: req.user.id
    }
  });

  if (!existingTransaction) {
    throw new AppError('Transaction not found', 404);
  }

  // If categoryId is being updated, verify it belongs to user and type matches
  if (updateData.categoryId) {
    const category = await prisma.category.findFirst({
      where: {
        id: updateData.categoryId,
        userId: req.user.id,
        isActive: true
      }
    });

    if (!category) {
      throw new AppError('Category not found or not accessible', 404);
    }

    const transactionType = updateData.type || existingTransaction.type;
    if (category.type !== transactionType) {
      throw new AppError('Category type does not match transaction type', 400);
    }
  }

  // Convert date if provided
  if (updateData.date) {
    updateData.date = new Date(updateData.date);
  }

  const transaction = await prisma.transaction.update({
    where: { id },
    data: updateData,
    include: {
      category: {
        select: {
          id: true,
          name: true,
          type: true
        }
      }
    }
  });

  res.json({
    success: true,
    message: 'Transaction updated successfully',
    data: { transaction }
  });
}));

/**
 * @swagger
 * /api/transactions/{id}:
 *   delete:
 *     summary: Delete transaction
 *     tags: [Transactions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Transaction deleted successfully
 *       404:
 *         description: Transaction not found
 */
router.delete('/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;

  // Check if transaction exists and belongs to user
  const transaction = await prisma.transaction.findFirst({
    where: {
      id,
      userId: req.user.id
    }
  });

  if (!transaction) {
    throw new AppError('Transaction not found', 404);
  }

  // Check if transaction is reconciled
  if (transaction.isReconciled) {
    throw new AppError('Cannot delete reconciled transaction', 400);
  }

  await prisma.transaction.delete({
    where: { id }
  });

  res.json({
    success: true,
    message: 'Transaction deleted successfully'
  });
}));

/**
 * @swagger
 * /api/transactions/dashboard:
 *   get:
 *     summary: Get dashboard statistics
 *     tags: [Transactions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [week, month, quarter, year]
 *           default: month
 *     responses:
 *       200:
 *         description: Dashboard statistics retrieved successfully
 */
router.get('/dashboard/stats', [
  query('period').optional().isIn(['week', 'month', 'quarter', 'year'])
], asyncHandler(async (req, res) => {
  const { period = 'month' } = req.query;

  // Calculate date range based on period
  const now = new Date();
  let startDate;

  switch (period) {
    case 'week':
      startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 7);
      break;
    case 'quarter':
      startDate = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3, 1);
      break;
    case 'year':
      startDate = new Date(now.getFullYear(), 0, 1);
      break;
    default: // month
      startDate = new Date(now.getFullYear(), now.getMonth(), 1);
  }

  const where = {
    userId: req.user.id,
    date: {
      gte: startDate,
      lte: now
    }
  };

  // Get current period stats
  const [incomeStats, expenseStats, recentTransactions] = await Promise.all([
    prisma.transaction.aggregate({
      where: { ...where, type: 'INCOME' },
      _sum: { amount: true },
      _count: true
    }),
    prisma.transaction.aggregate({
      where: { ...where, type: 'EXPENSE' },
      _sum: { amount: true },
      _count: true
    }),
    prisma.transaction.findMany({
      where: { userId: req.user.id },
      include: {
        category: {
          select: { name: true, type: true }
        }
      },
      orderBy: { date: 'desc' },
      take: 5
    })
  ]);

  // Get previous period for comparison
  const prevStartDate = new Date(startDate);
  const prevEndDate = new Date(startDate);

  switch (period) {
    case 'week':
      prevStartDate.setDate(prevStartDate.getDate() - 7);
      break;
    case 'quarter':
      prevStartDate.setMonth(prevStartDate.getMonth() - 3);
      break;
    case 'year':
      prevStartDate.setFullYear(prevStartDate.getFullYear() - 1);
      break;
    default: // month
      prevStartDate.setMonth(prevStartDate.getMonth() - 1);
  }

  const prevWhere = {
    userId: req.user.id,
    date: {
      gte: prevStartDate,
      lt: startDate
    }
  };

  const [prevIncomeStats, prevExpenseStats] = await Promise.all([
    prisma.transaction.aggregate({
      where: { ...prevWhere, type: 'INCOME' },
      _sum: { amount: true }
    }),
    prisma.transaction.aggregate({
      where: { ...prevWhere, type: 'EXPENSE' },
      _sum: { amount: true }
    })
  ]);

  const currentIncome = incomeStats._sum.amount || 0;
  const currentExpense = expenseStats._sum.amount || 0;
  const prevIncome = prevIncomeStats._sum.amount || 0;
  const prevExpense = prevExpenseStats._sum.amount || 0;

  res.json({
    success: true,
    data: {
      period,
      current: {
        income: currentIncome,
        expense: currentExpense,
        net: currentIncome - currentExpense,
        transactionCount: incomeStats._count + expenseStats._count
      },
      previous: {
        income: prevIncome,
        expense: prevExpense,
        net: prevIncome - prevExpense
      },
      changes: {
        income: prevIncome > 0 ? ((currentIncome - prevIncome) / prevIncome * 100) : 0,
        expense: prevExpense > 0 ? ((currentExpense - prevExpense) / prevExpense * 100) : 0,
        net: (prevIncome - prevExpense) !== 0 ? (((currentIncome - currentExpense) - (prevIncome - prevExpense)) / Math.abs(prevIncome - prevExpense) * 100) : 0
      },
      recentTransactions
    }
  });
}));

module.exports = router;
