{"name": "app-ke-toan-server", "version": "1.0.0", "description": "Backend API cho ứng dụng kế toán TT 133", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest", "test:watch": "jest --watch", "db:migrate": "prisma migrate dev", "db:seed": "node prisma/seed.js", "db:studio": "prisma studio", "db:generate": "prisma generate", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "express-rate-limit": "^7.1.5", "@prisma/client": "^5.6.0", "prisma": "^5.6.0", "googleapis": "^128.0.0", "google-auth-library": "^9.4.1", "nodemailer": "^6.9.7", "node-cron": "^3.0.3", "multer": "^1.4.5-lts.1", "express-validator": "^7.0.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "compression": "^1.7.4"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.54.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-n": "^16.3.1", "eslint-plugin-promise": "^6.1.1"}, "keywords": ["accounting", "api", "vietnam", "thong-tu-133"], "author": "Accounting App Team", "license": "MIT"}