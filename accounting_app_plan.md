# Kế hoạch phát triển App quản lý kế toán theo Thông tư 133

## Tổng quan dự án

**Mục tiêu:** Xây dựng app web quản lý kế toán đơn gi<PERSON>n, thân thiện với người dùng theo Thông tư 133/2016/TT-BTC

**Công nghệ chính:**
- Frontend: React.js với UI/UX tối ưu
- Backend: Node.js/Express hoặc Python/Django
- Database: PostgreSQL hoặc MongoDB
- Tích hợp: Google Sheets API
- Deployment: Vercel/Netlify + Railway/Heroku

---

## GIAI ĐOẠN 1: Nền tảng c<PERSON> bản (2-3 tuần)

### 1.1 Thiết kế giao diện và UX

**Yêu cầu cho AI:**
```
Tạo wireframe và mockup cho:
- Dashboard tổng quan dòng tiền
- Form nhập thu/chi với wizard 
- <PERSON><PERSON><PERSON> hình phân loại giao dịch
- Giao diện kết nối Google Sheets
- Responsive design cho mobile/tablet
```

**Deliverables:**
- Figma/Sketch designs
- Component library (buttons, forms, cards)
- Color scheme và typography
- User flow diagram

### 1.2 Xây dựng backend API

**Yêu cầu cho AI:**
```
Tạo RESTful API với các endpoint:
POST /api/transactions - Tạo giao dịch mới
GET /api/transactions - Lấy danh sách giao dịch
PUT /api/transactions/:id - Cập nhật giao dịch
DELETE /api/transactions/:id - Xóa giao dịch
GET /api/categories - Lấy danh mục thu/chi
POST /api/google-sync - Đồng bộ Google Sheets

Cấu trúc database:
- Table: transactions (id, type, amount, category, description, date, notes, formula_details)
- Table: categories (id, name, type, parent_id)
- Table: users (id, email, google_sheet_id, settings)
```

### 1.3 Frontend React App

**Yêu cầu cho AI:**
```
Tạo React app với:
1. Dashboard hiển thị:
   - Tổng thu trong tháng
   - Tổng chi trong tháng  
   - Dòng tiền ròng
   - Biểu đồ xu hướng 7 ngày gần nhất

2. Form nhập giao dịch (Wizard):
   - Bước 1: Chọn loại (Thu/Chi)
   - Bước 2: Chọn danh mục
   - Bước 3: Nhập số tiền và mô tả
   - Bước 4: Thêm ghi chú và công thức (optional)
   - Bước 5: Xác nhận

3. Danh sách giao dịch:
   - Filter theo ngày, loại, danh mục
   - Sắp xếp theo ngày, số tiền
   - Search theo mô tả
   - Edit/Delete inline

4. Quản lý danh mục:
   - CRUD danh mục thu/chi
   - Drag & drop để sắp xếp
```

### 1.4 Module cảnh báo và automation

**Yêu cầu cho AI:**
```
Tạo hệ thống cảnh báo thông minh:
1. Email/SMS nhắc nhở trước hạn nộp thuế 3-5 ngày
2. Notification trong app khi có giao dịch bất thường
3. Auto-calculation thuế khi nhập giao dịch
4. Backup data định kỳ lên Google Drive
5. Weekly/Monthly summary report

Cấu hình notification:
- Thuế GTGT: Nhắc trước ngày 15 và 20 mỗi tháng
- Thuế TNDN: Nhắc trước ngày 25 cuối quý  
- Thuế môn bài: Nhắc từ đầu tháng 1
- Báo cáo tài chính: Nhắc cuối mỗi quý
```

### 1.5 Tích hợp Google Sheets

**Yêu cầu cho AI:**
```
Tạo module đồng bộ Google Sheets:
1. OAuth2 authentication với Google
2. Tạo/kết nối Google Sheet template
3. Format sheet theo chuẩn:
   - Sheet "Thu": Ngày | Mô tả | Danh mục | Số tiền | Ghi chú
   - Sheet "Chi": Ngày | Mô tả | Danh mục | Số tiền | Ghi chú  
   - Sheet "Tổng hợp": Thống kê theo tháng/quý
4. Real-time sync: App → Google Sheets
5. Import từ Google Sheets → App (future)
```

**Thư viện cần dùng:**
- googleapis npm package
- google-auth-library
- React Google Login

---

## GIAI ĐOẠN 2: Nghiệp vụ kế toán (3-4 tuần)

### 2.1 Hệ thống tài khoản kế toán

**Yêu cầu cho AI:**
```
Tạo chart of accounts theo Thông tư 133:
- Tài sản (1xx): 111, 112, 131, 141, 152, 153...
- Nguồn vốn (2xx): 211, 221, 241...
- Doanh thu (5xx): 511, 515...
- Chi phí (6xx): 621, 622, 627, 642...

Database mở rộng:
- Table: accounts (code, name, type, parent_code, level)
- Table: journal_entries (id, date, description, reference)
- Table: journal_details (entry_id, account_code, debit, credit, description)
- Table: tax_config (tax_type, rate, description, applicable_accounts)
- Table: tax_obligations (period, tax_type, base_amount, tax_amount, due_date, status)

Bổ sung tài khoản thuế:
- TK 1331: Thuế GTGT được khấu trừ
- TK 3331: Thuế GTGT phải nộp  
- TK 3332: Thuế GTGT phải thu hồi
- TK 8211: Thuế thu nhập doanh nghiệp
- TK 3335: Thuế thu nhập cá nhân
- TK 3336: Thuế môn bài
- TK 3338: Thuế tài nguyên, phí, lệ phí khác

Tự động tạo bút toán cho từng loại giao dịch:
- Thu vốn: Nợ 111, Có 411
- Chi vốn: Nợ 111, Có các tài khoản tương ứng
- Thu dịch vụ: Nợ 111, Có 511
- Chi lương: Nợ 621, Có 111 + 334
- Chi BHXH: Nợ 627, Có 111
- Chi thuế: Nợ 8xx, Có 111
```

### 2.2 Hệ thống thuế và nghĩa vụ tài chính

**Yêu cầu cho AI:**
```
Tạo module quản lý thuế toàn diện:

Database mở rộng:
- Table: tax_config (tax_type, rate, description, applicable_accounts)
- Table: tax_obligations (period, tax_type, base_amount, tax_amount, due_date, status)
- Table: tax_declarations (id, period, tax_type, declaration_data, submitted_date)

Cấu hình thuế suất:
1. Thuế GTGT:
   - 0%: Hàng xuất khẩu, một số dịch vụ đặc biệt
   - 5%: Nước sạch, dịch vụ giáo dục, y tế, một số dịch vụ
   - 10%: Hàng hóa và dịch vụ thông thường
   
2. Thuế TNDN: 20% (có thể 10% cho DN nhỏ)

3. Các loại thuế khác:
   - Thuế môn bài: Theo quy mô DN
   - Thuế tài nguyên: Tùy loại tài nguyên
   - Phí bảo vệ môi trường
   - Lệ phí trước bạ
```

### 2.3 Wizard nghiệp vụ tích hợp thuế

**Yêu cầu cho AI:**
```
Nâng cấp wizard với logic thuế tự động:

1. Thu vốn:
   - Input: Số tiền, nguồn vốn
   - Auto generate: Bút toán Nợ 111/Có 411
   - Ghi chú: "Công thức: Tiền mặt tăng = Vốn chủ sở hữu tăng"

2. Thu dịch vụ (có thuế GTGT):
   - Input: Tổng tiền nhận, loại dịch vụ
   - Auto detect: Thuế suất GTGT (0%, 5%, 10%)
   - Auto calculate: 
     * Doanh thu chưa thuế = Tiền nhận / (1 + thuế suất)
     * Thuế GTGT = Doanh thu chưa thuế × thuế suất
   - Auto generate: 
     * Nợ TK 111: Tổng tiền nhận
     * Có TK 511: Doanh thu chưa thuế  
     * Có TK 3331: Thuế GTGT phải nộp
   - Ghi chú: "VD: Nhận 105,000 → DT 100,000 + Thuế 5,000 (5%)"

3. Chi mua hàng/dịch vụ (có thuế GTGT đầu vào):
   - Input: Hóa đơn có thuế, loại chi phí
   - Auto calculate: Chi phí gốc, thuế GTGT được khấu trừ
   - Auto generate:
     * Nợ TK 621/622: Chi phí chưa thuế
     * Nợ TK 1331: Thuế GTGT được khấu trừ
     * Có TK 111: Tổng tiền chi
   - Ghi chú: "Điều kiện khấu trừ: HĐ hợp lệ + phục vụ sản xuất kinh doanh"

4. Chi lương (có thuế TNCN):
   - Input: Lương gross, số người, giảm trừ
   - Auto calculate: 
     * BHXH (8%), BHYT (1.5%), BHTN (1%)
     * Thu nhập tính thuế = Gross - BHXH - Giảm trừ bản thân (11tr) - Giảm trừ người phụ thuộc
     * Thuế TNCN theo biểu lũy tiến
   - Auto generate: Bút toán chi lương + thuế + BHXH
   - Ghi chú: "Công thức tính thuế TNCN chi tiết"

5. Nộp thuế:
   - Input: Loại thuế, kỳ thuế, số tiền
   - Auto generate:
     * Nợ TK 3331 (Thuế GTGT): Số thuế nộp
     * Nợ TK 8211 (Thuế TNDN): Số thuế nộp  
     * Có TK 111: Tiền nộp
   - Auto update: Trạng thái nghĩa vụ thuế
```

### 2.3 Validation và kiểm tra

**Yêu cầu cho AI:**
```
Tạo hệ thống validation:
1. Kiểm tra cân đối kế toán (Tổng Nợ = Tổng Có)
2. Validate logic nghiệp vụ
3. Cảnh báo khi số liệu bất thường
4. Suggest fix khi có lỗi

UI hiển thị:
- Dashboard "Tình trạng sổ sách"
- Alert khi có lỗi cân đối
- Tooltip giải thích từng bút toán
```

---

## GIAI ĐOẠN 3: Báo cáo và thống kê (2-3 tuần)

### 3.1 Báo cáo thuế và nghĩa vụ tài chính

**Yêu cầu cho AI:**
```
Tạo hệ thống báo cáo thuế tự động:

1. Tờ khai thuế GTGT (Mẫu 01/GTGT):
   - Doanh thu chịu thuế các mức 0%, 5%, 10%
   - Thuế GTGT đầu ra
   - Thuế GTGT đầu vào được khấu trừ
   - Số thuế phải nộp = Thuế đầu ra - Thuế đầu vào
   - Auto fill form theo template Tổng cục Thuế

2. Tờ khai thuế TNDN (Mẫu 03/TNDN):
   - Tổng doanh thu
   - Các khoản chi phí hợp lý, hợp lệ
   - Thu nhập chịu thuế
   - Thuế TNDN phải nộp (20%)
   - Số thuế đã nộp trong kỳ
   - Số thuế còn phải nộp/được hoàn

3. Bảng kê hoá đơn bán ra/mua vào:
   - Xuất file Excel theo format chuẩn
   - Validation số hóa đơn, mã số thuế
   - Tổng hợp theo từng loại thuế suất

4. Dashboard nghĩa vụ thuế:
   - Lịch nộp thuế (GTGT: cuối tháng, TNDN: cuối quý)
   - Cảnh báo sắp đến hạn nộp thuế
   - Tracking trạng thái đã nộp/chưa nộp
   - Tính toán tiền phạt chậm nộp (nếu có)

5. Báo cáo thuế môn bài:
   - Tự động phân loại quy mô DN
   - Tính mức thuế phải nộp
   - Nhắc nhở thời hạn nộp (trước 30/1 hàng năm)
```

### 3.2 Báo cáo tài chính cơ bản

**Yêu cầu cho AI:**
```
Tạo các báo cáo theo Thông tư 133:

1. Báo cáo kết quả kinh doanh:
   - Doanh thu thuần
   - Các khoản giảm trừ doanh thu  
   - Chi phí kinh doanh
   - Lợi nhuận trước thuế
   - Lợi nhuận sau thuế

2. Bảng cân đối kế toán đơn giản:
   - Tài sản ngắn hạn/dài hạn
   - Nợ phải trả
   - Vốn chủ sở hữu

3. Báo cáo lưu chuyển tiền tệ:
   - Lưu chuyển tiền từ hoạt động kinh doanh
   - Lưu chuyển tiền từ hoạt động đầu tư
   - Lưu chuyển tiền từ hoạt động tài chính

Export formats: PDF, Excel, Google Sheets
```

### 3.2 Dashboard analytics

**Yêu cầu cho AI:**
```
Tạo dashboard phân tích:
1. Biểu đồ doanh thu/chi phí theo tháng
2. Phân tích cơ cấu chi phí (pie chart)
3. Xu hướng lợi nhuận (line chart) 
4. So sánh với kỳ trước (% change)
5. Dự báo dòng tiền 3 tháng tới (basic linear regression)

Sử dụng thư viện: Chart.js hoặc Recharts
```

### 3.3 Kết chuyển và khóa sổ

**Yêu cầu cho AI:**
```
Tạo chức năng:
1. Kết chuyển cuối kỳ:
   - Kết chuyển doanh thu (Nợ 511/Có 911)
   - Kết chuyển chi phí (Nợ 911/Có 6xx)
   - Xác định kết quả kinh doanh (Nợ 911/Có 421)

2. Khóa sổ kế toán:
   - Lock toàn bộ giao dịch trong kỳ
   - Generate backup data
   - Tạo kỳ kế toán mới

3. Note system nâng cao:
   - Rich text editor cho ghi chú
   - Template ghi chú cho từng nghiệp vụ
   - Version history cho mỗi bút toán
```

---

## Workflow làm việc với AI

### Chuẩn bị cho AI
```
1. Cung cấp requirements chi tiết cho từng task
2. Include sample data và expected output
3. Specify coding standards và best practices
4. Provide API documentation template
5. Include test cases và acceptance criteria
```

### Quy trình review và feedback
```
1. AI tạo MVP cho từng module
2. Manual testing và feedback
3. AI refine theo feedback
4. Integration testing
5. Deploy và user testing
6. Bug fixes và optimization
```

### Tools hỗ trợ
```
- Project management: Linear hoặc Notion
- Code review: GitHub với clear PR template
- Testing: Jest + Cypress cho e2e testing
- Documentation: Gitbook hoặc Notion
- Design handoff: Figma to React plugins
```

---

## Timeline tổng thể

**Tuần 1-3:** Giai đoạn 1 - MVP cơ bản
**Tuần 4-7:** Giai đoạn 2 - Nghiệp vụ kế toán  
**Tuần 8-10:** Giai đoạn 3 - Báo cáo và hoàn thiện
**Tuần 11-12:** Testing, bug fixes, deployment

## Compliance và cập nhật quy định

### Hệ thống cập nhật thuế suất
```
Tạo admin panel để cập nhật:
- Thuế suất GTGT theo ngành nghề
- Biểu thuế TNCN lũy tiến  
- Mức giảm trừ gia cảnh
- Thuế môn bài theo quy mô DN
- Các loại phí, lệ phí mới

Config file cho tax rates:
{
  "vat_rates": {
    "services_education": 0,
    "services_healthcare": 5, 
    "services_general": 10,
    "goods_export": 0,
    "goods_general": 10
  },
  "corporate_tax": 20,
  "personal_tax_brackets": [
    {"from": 0, "to": 5000000, "rate": 5},
    {"from": 5000001, "to": 10000000, "rate": 10},
    // ...
  ],
  "deductions": {
    "personal": 11000000,
    "dependent": 4400000
  }
}
```

### Template hóa đơn và chứng từ
```
Tạo templates chuẩn:
- Hóa đơn GTGT (form 01/GTGT)
- Hóa đơn bán hàng (form 02/BH)  
- Phiếu thu, phiếu chi
- Bảng kê thuế GTGT
- Tờ khai thuế TNDN

Export multiple formats: PDF, Excel, XML (cho HTKK)
```

## Rủi ro và mitigation

**Rủi ro kỹ thuật:**
- Google Sheets API limits → Implement caching và batch processing
- Performance với large dataset → Implement pagination và lazy loading
- Tax calculation errors → Multiple validation layers và audit trail

**Rủi ro nghiệp vụ:**
- Thay đổi quy định thuế → Design flexible tax config system
- Sai sót trong tính thuế → Auto-check với external tax calculator API
- Feedback người dùng → Plan for iterative development

**Rủi ro pháp lý:**
- Compliance với quy định mới → Subscribe to tax regulation updates
- Audit trail requirements → Complete transaction logging
- Data security → Encryption và secure backup

**Rủi ro thời gian:**
- AI learning curve → Provide detailed examples và reference code
- Tax complexity → Phase approach: basic → advanced features
- Scope creep → Strictly follow phased approach