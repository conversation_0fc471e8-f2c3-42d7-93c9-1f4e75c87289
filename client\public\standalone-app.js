// Standalone Accounting App - Client-side with Google Sheets integration
class AccountingApp {
    constructor() {
        this.transactions = JSON.parse(localStorage.getItem('transactions') || '[]');
        this.categories = {
            income: [
                '<PERSON><PERSON><PERSON> thu bán hàng',
                '<PERSON><PERSON><PERSON> thu dịch vụ',
                '<PERSON><PERSON> nhập kh<PERSON>',
                '<PERSON><PERSON><PERSON> tiề<PERSON> gửi',
                '<PERSON>hu từ đầu tư'
            ],
            expense: [
                'Chi phí nguyên vật liệu',
                '<PERSON> phí nhân công',
                'Chi phí văn phòng',
                'Chi phí marketing',
                'Chi phí thuế',
                '<PERSON> phí khác'
            ]
        };

        // Google Sheets configuration
        this.googleConfig = {
            apiKey: 'YOUR_API_KEY', // Sẽ được cấu hình
            clientId: 'YOUR_CLIENT_ID', // Sẽ được cấu hình
            discoveryDoc: 'https://sheets.googleapis.com/$discovery/rest?version=v4',
            scopes: 'https://www.googleapis.com/auth/spreadsheets'
        };

        this.googleAuth = null;
        this.spreadsheetId = localStorage.getItem('spreadsheetId');
        this.syncSettings = JSON.parse(localStorage.getItem('syncSettings') || '{"autoSync": "manual", "sheetName": "Kế toán TT133"}');

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.updateCategories();
        this.updateDashboard();
        this.setDefaultDate();
        this.initGoogleAPI();
        this.updateGoogleSheetsUI();
    }

    setupEventListeners() {
        // Transaction form submission
        document.getElementById('transactionForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.addTransaction();
        });

        // Transaction type change
        document.getElementById('transactionType').addEventListener('change', () => {
            this.updateCategories();
        });
    }

    setDefaultDate() {
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('date').value = today;
    }

    updateCategories() {
        const type = document.getElementById('transactionType').value;
        const categorySelect = document.getElementById('category');

        categorySelect.innerHTML = '<option value="">Chọn danh mục</option>';

        if (type && this.categories[type]) {
            this.categories[type].forEach(category => {
                const option = document.createElement('option');
                option.value = category;
                option.textContent = category;
                categorySelect.appendChild(option);
            });
        }
    }

    addTransaction() {
        const formData = {
            id: Date.now(),
            type: document.getElementById('transactionType').value,
            description: document.getElementById('description').value,
            amount: parseFloat(document.getElementById('amount').value),
            date: document.getElementById('date').value,
            category: document.getElementById('category').value,
            notes: document.getElementById('notes').value,
            createdAt: new Date().toISOString()
        };

        // Validation
        if (!formData.type || !formData.description || !formData.amount || !formData.date || !formData.category) {
            alert('Vui lòng điền đầy đủ thông tin bắt buộc!');
            return;
        }

        if (formData.amount <= 0) {
            alert('Số tiền phải lớn hơn 0!');
            return;
        }

        // Add transaction
        this.transactions.push(formData);
        this.saveTransactions();
        this.updateDashboard();
        this.clearForm();

        // Auto sync if enabled
        if (this.syncSettings.autoSync === 'immediate' && this.spreadsheetId) {
            this.syncToGoogleSheets();
        }

        // Show success message
        alert('Đã thêm giao dịch thành công!');

        // Switch to dashboard
        showSection('dashboard');
    }

    clearForm() {
        document.getElementById('transactionForm').reset();
        this.setDefaultDate();
        this.updateCategories();
    }

    saveTransactions() {
        localStorage.setItem('transactions', JSON.stringify(this.transactions));
    }

    updateDashboard() {
        const currentMonth = new Date().getMonth();
        const currentYear = new Date().getFullYear();

        // Filter transactions for current month
        const monthlyTransactions = this.transactions.filter(t => {
            const transactionDate = new Date(t.date);
            return transactionDate.getMonth() === currentMonth &&
                   transactionDate.getFullYear() === currentYear;
        });

        // Calculate totals
        const totalIncome = monthlyTransactions
            .filter(t => t.type === 'income')
            .reduce((sum, t) => sum + t.amount, 0);

        const totalExpense = monthlyTransactions
            .filter(t => t.type === 'expense')
            .reduce((sum, t) => sum + t.amount, 0);

        const netProfit = totalIncome - totalExpense;

        // Update dashboard stats
        document.getElementById('totalIncome').textContent = this.formatCurrency(totalIncome);
        document.getElementById('totalExpense').textContent = this.formatCurrency(totalExpense);
        document.getElementById('netProfit').textContent = this.formatCurrency(netProfit);
        document.getElementById('transactionCount').textContent = monthlyTransactions.length;

        // Update recent transactions
        this.updateRecentTransactions();
        this.updateAllTransactions();
    }

    updateRecentTransactions() {
        const container = document.getElementById('recentTransactions');
        const recentTransactions = this.transactions
            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
            .slice(0, 5);

        if (recentTransactions.length === 0) {
            container.innerHTML = `
                <p style="text-align: center; color: #666; padding: 2rem;">
                    Chưa có giao dịch nào. Hãy thêm giao dịch đầu tiên!
                </p>
            `;
            return;
        }

        container.innerHTML = recentTransactions.map(transaction => `
            <div class="transaction-item">
                <div>
                    <div style="font-weight: 500;">${transaction.description}</div>
                    <div style="font-size: 0.9rem; color: #666;">
                        ${transaction.category} • ${this.formatDate(transaction.date)}
                    </div>
                </div>
                <div class="${transaction.type === 'income' ? 'amount-positive' : 'amount-negative'}">
                    ${transaction.type === 'income' ? '+' : '-'}${this.formatCurrency(transaction.amount)}
                </div>
            </div>
        `).join('');
    }

    updateAllTransactions() {
        const container = document.getElementById('allTransactions');
        const allTransactions = this.transactions
            .sort((a, b) => new Date(b.date) - new Date(a.date));

        if (allTransactions.length === 0) {
            container.innerHTML = `
                <p style="text-align: center; color: #666; padding: 2rem;">
                    Chưa có giao dịch nào
                </p>
            `;
            return;
        }

        container.innerHTML = allTransactions.map(transaction => `
            <div class="transaction-item">
                <div>
                    <div style="font-weight: 500;">${transaction.description}</div>
                    <div style="font-size: 0.9rem; color: #666;">
                        ${transaction.category} • ${this.formatDate(transaction.date)}
                        ${transaction.notes ? ` • ${transaction.notes}` : ''}
                    </div>
                </div>
                <div class="${transaction.type === 'income' ? 'amount-positive' : 'amount-negative'}">
                    ${transaction.type === 'income' ? '+' : '-'}${this.formatCurrency(transaction.amount)}
                </div>
            </div>
        `).join('');
    }

    formatCurrency(amount) {
        return new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(amount);
    }

    formatDate(dateString) {
        return new Date(dateString).toLocaleDateString('vi-VN');
    }

    // Google Sheets Integration Methods
    async initGoogleAPI() {
        try {
            // For demo purposes, we'll use a simplified approach
            // In production, you would need to set up proper Google API credentials
            console.log('Google API initialized (demo mode)');
        } catch (error) {
            console.error('Failed to initialize Google API:', error);
        }
    }

    updateGoogleSheetsUI() {
        const isConnected = !!this.spreadsheetId;
        document.getElementById('sheets-disconnected').classList.toggle('hidden', isConnected);
        document.getElementById('sheets-connected').classList.toggle('hidden', !isConnected);

        if (isConnected) {
            const lastSync = localStorage.getItem('lastSync');
            const syncInfo = lastSync ?
                `Lần cuối: ${new Date(lastSync).toLocaleString('vi-VN')}` :
                'Chưa đồng bộ lần nào';
            document.getElementById('sync-info').textContent = syncInfo;
        }

        // Update sync settings
        document.getElementById('autoSync').value = this.syncSettings.autoSync;
        document.getElementById('sheetName').value = this.syncSettings.sheetName;
    }

    async connectGoogleSheets() {
        try {
            // Demo implementation - in real app, this would use Google OAuth
            const confirmed = confirm(
                'Tính năng này đang ở chế độ demo.\n\n' +
                'Trong phiên bản thực tế, bạn sẽ:\n' +
                '1. Đăng nhập Google\n' +
                '2. Cấp quyền truy cập Google Sheets\n' +
                '3. Tự động tạo spreadsheet mới\n\n' +
                'Bạn có muốn tiếp tục với demo không?'
            );

            if (!confirmed) return;

            // Simulate creating a spreadsheet
            const mockSpreadsheetId = 'demo_' + Date.now();
            this.spreadsheetId = mockSpreadsheetId;
            localStorage.setItem('spreadsheetId', mockSpreadsheetId);

            // Create demo spreadsheet structure
            await this.createSpreadsheetStructure();

            this.updateGoogleSheetsUI();
            alert('✅ Đã kết nối thành công với Google Sheets (Demo)!\n\nTrong phiên bản thực tế, một Google Sheet mới sẽ được tạo tự động.');

        } catch (error) {
            console.error('Error connecting to Google Sheets:', error);
            alert('❌ Lỗi kết nối Google Sheets: ' + error.message);
        }
    }

    async createSpreadsheetStructure() {
        // In real implementation, this would create actual Google Sheets
        const structure = {
            title: this.syncSettings.sheetName,
            sheets: [
                {
                    title: 'Thu nhập',
                    headers: ['Ngày', 'Mô tả', 'Danh mục', 'Số tiền', 'Ghi chú', 'Tham chiếu']
                },
                {
                    title: 'Chi phí',
                    headers: ['Ngày', 'Mô tả', 'Danh mục', 'Số tiền', 'Ghi chú', 'Tham chiếu']
                },
                {
                    title: 'Tổng hợp',
                    headers: ['Tháng', 'Tổng thu', 'Tổng chi', 'Lợi nhuận', 'Số giao dịch']
                }
            ]
        };

        localStorage.setItem('spreadsheetStructure', JSON.stringify(structure));
        console.log('Created spreadsheet structure:', structure);
    }

    async syncToGoogleSheets() {
        if (!this.spreadsheetId) {
            alert('Chưa kết nối Google Sheets!');
            return;
        }

        try {
            // Show loading
            const syncButton = document.querySelector('button[onclick="syncToSheets()"]');
            const originalText = syncButton.innerHTML;
            syncButton.innerHTML = '<span class="material-icons">sync</span> Đang đồng bộ...';
            syncButton.disabled = true;

            // Simulate sync process
            await this.simulateSync();

            // Update last sync time
            const now = new Date().toISOString();
            localStorage.setItem('lastSync', now);

            // Update UI
            this.updateGoogleSheetsUI();

            // Restore button
            syncButton.innerHTML = originalText;
            syncButton.disabled = false;

            alert('✅ Đồng bộ thành công!\n\n' +
                  `Đã đồng bộ ${this.transactions.length} giao dịch lên Google Sheets.`);

        } catch (error) {
            console.error('Sync error:', error);
            alert('❌ Lỗi đồng bộ: ' + error.message);
        }
    }

    async simulateSync() {
        // Simulate API calls with delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        // In real implementation, this would:
        // 1. Clear existing data in sheets
        // 2. Upload income transactions to "Thu nhập" sheet
        // 3. Upload expense transactions to "Chi phí" sheet
        // 4. Generate summary data for "Tổng hợp" sheet

        const incomeTransactions = this.transactions.filter(t => t.type === 'income');
        const expenseTransactions = this.transactions.filter(t => t.type === 'expense');

        console.log('Syncing to Google Sheets:', {
            income: incomeTransactions.length,
            expense: expenseTransactions.length,
            total: this.transactions.length
        });

        // Store sync data for demo
        localStorage.setItem('lastSyncData', JSON.stringify({
            timestamp: new Date().toISOString(),
            transactionCount: this.transactions.length,
            incomeCount: incomeTransactions.length,
            expenseCount: expenseTransactions.length
        }));
    }

    openGoogleSheet() {
        if (!this.spreadsheetId) {
            alert('Chưa kết nối Google Sheets!');
            return;
        }

        // In real implementation, this would open the actual Google Sheet
        const demoUrl = `https://docs.google.com/spreadsheets/d/${this.spreadsheetId}/edit`;

        alert('📊 Trong phiên bản thực tế, Google Sheet sẽ được mở tại:\n\n' + demoUrl + '\n\n' +
              'Demo: Hiển thị cấu trúc spreadsheet trong console.');

        // Show structure in console for demo
        const structure = JSON.parse(localStorage.getItem('spreadsheetStructure') || '{}');
        console.log('Google Sheet Structure:', structure);
        console.log('Current Data:', this.transactions);
    }

    disconnectGoogleSheets() {
        const confirmed = confirm('Bạn có chắc muốn ngắt kết nối Google Sheets?\n\nDữ liệu local sẽ được giữ nguyên.');

        if (confirmed) {
            this.spreadsheetId = null;
            localStorage.removeItem('spreadsheetId');
            localStorage.removeItem('lastSync');
            localStorage.removeItem('spreadsheetStructure');
            localStorage.removeItem('lastSyncData');

            this.updateGoogleSheetsUI();
            alert('✅ Đã ngắt kết nối Google Sheets thành công!');
        }
    }

    updateSyncSettings() {
        this.syncSettings.autoSync = document.getElementById('autoSync').value;
        this.syncSettings.sheetName = document.getElementById('sheetName').value;

        localStorage.setItem('syncSettings', JSON.stringify(this.syncSettings));

        alert('✅ Đã cập nhật cài đặt đồng bộ!');
    }
}

// Navigation functions
function showSection(sectionId) {
    // Hide all sections
    document.querySelectorAll('.section').forEach(section => {
        section.classList.add('hidden');
    });

    // Show selected section
    document.getElementById(sectionId).classList.remove('hidden');

    // Update navigation
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });

    event.target.closest('.nav-link').classList.add('active');
}

// Tax calculation function
function calculateVAT() {
    const revenue = parseFloat(document.getElementById('revenue').value) || 0;
    const vatRate = parseFloat(document.getElementById('vatRate').value) || 0;

    if (revenue <= 0) {
        alert('Vui lòng nhập doanh thu hợp lệ!');
        return;
    }

    const vatAmount = revenue * (vatRate / 100);
    const totalAmount = revenue + vatAmount;

    const resultContainer = document.getElementById('vatResult');
    resultContainer.innerHTML = `
        <div style="background: #f8f9fa; padding: 1rem; border-radius: 8px; border-left: 4px solid #3498db;">
            <h4>Kết quả tính thuế GTGT:</h4>
            <p><strong>Doanh thu chưa thuế:</strong> ${app.formatCurrency(revenue)}</p>
            <p><strong>Thuế GTGT (${vatRate}%):</strong> ${app.formatCurrency(vatAmount)}</p>
            <p><strong>Tổng tiền phải thu:</strong> ${app.formatCurrency(totalAmount)}</p>
            <hr style="margin: 1rem 0;">
            <p><em>Công thức: Doanh thu × ${vatRate}% = ${app.formatCurrency(vatAmount)}</em></p>
        </div>
    `;
}

// Google Sheets functions (called from HTML)
function connectGoogleSheets() {
    app.connectGoogleSheets();
}

function syncToSheets() {
    app.syncToGoogleSheets();
}

function openGoogleSheet() {
    app.openGoogleSheet();
}

function disconnectGoogleSheets() {
    app.disconnectGoogleSheets();
}

function updateSyncSettings() {
    app.updateSyncSettings();
}

// Initialize app when page loads
let app;
document.addEventListener('DOMContentLoaded', () => {
    app = new AccountingApp();

    // Show welcome message for first-time users
    if (app.transactions.length === 0) {
        setTimeout(() => {
            alert('Chào mừng bạn đến với App Kế toán TT 133!\n\n' +
                  'Phiên bản này có tích hợp Google Sheets!\n\n' +
                  '✨ Tính năng mới:\n' +
                  '• Đồng bộ dữ liệu lên Google Sheets\n' +
                  '• Tự động tạo báo cáo\n' +
                  '• Truy cập từ mọi nơi\n\n' +
                  'Hãy bắt đầu bằng cách thêm giao dịch đầu tiên!');
        }, 1000);
    }
});
