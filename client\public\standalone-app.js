// Standalone Accounting App - Client-side only
class AccountingApp {
    constructor() {
        this.transactions = JSON.parse(localStorage.getItem('transactions') || '[]');
        this.categories = {
            income: [
                '<PERSON><PERSON><PERSON> thu bán hàng',
                '<PERSON><PERSON><PERSON> thu dịch vụ',
                '<PERSON><PERSON> nh<PERSON><PERSON> kh<PERSON>',
                '<PERSON><PERSON><PERSON> tiền gửi',
                'Thu từ đầu tư'
            ],
            expense: [
                'Chi phí nguyên vật liệu',
                '<PERSON> phí nhân công',
                'Chi phí văn phòng',
                'Chi phí marketing',
                'Chi phí thuế',
                'Chi phí khác'
            ]
        };
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.updateCategories();
        this.updateDashboard();
        this.setDefaultDate();
    }
    
    setupEventListeners() {
        // Transaction form submission
        document.getElementById('transactionForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.addTransaction();
        });
        
        // Transaction type change
        document.getElementById('transactionType').addEventListener('change', () => {
            this.updateCategories();
        });
    }
    
    setDefaultDate() {
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('date').value = today;
    }
    
    updateCategories() {
        const type = document.getElementById('transactionType').value;
        const categorySelect = document.getElementById('category');
        
        categorySelect.innerHTML = '<option value="">Chọn danh mục</option>';
        
        if (type && this.categories[type]) {
            this.categories[type].forEach(category => {
                const option = document.createElement('option');
                option.value = category;
                option.textContent = category;
                categorySelect.appendChild(option);
            });
        }
    }
    
    addTransaction() {
        const formData = {
            id: Date.now(),
            type: document.getElementById('transactionType').value,
            description: document.getElementById('description').value,
            amount: parseFloat(document.getElementById('amount').value),
            date: document.getElementById('date').value,
            category: document.getElementById('category').value,
            notes: document.getElementById('notes').value,
            createdAt: new Date().toISOString()
        };
        
        // Validation
        if (!formData.type || !formData.description || !formData.amount || !formData.date || !formData.category) {
            alert('Vui lòng điền đầy đủ thông tin bắt buộc!');
            return;
        }
        
        if (formData.amount <= 0) {
            alert('Số tiền phải lớn hơn 0!');
            return;
        }
        
        // Add transaction
        this.transactions.push(formData);
        this.saveTransactions();
        this.updateDashboard();
        this.clearForm();
        
        // Show success message
        alert('Đã thêm giao dịch thành công!');
        
        // Switch to dashboard
        showSection('dashboard');
    }
    
    clearForm() {
        document.getElementById('transactionForm').reset();
        this.setDefaultDate();
        this.updateCategories();
    }
    
    saveTransactions() {
        localStorage.setItem('transactions', JSON.stringify(this.transactions));
    }
    
    updateDashboard() {
        const currentMonth = new Date().getMonth();
        const currentYear = new Date().getFullYear();
        
        // Filter transactions for current month
        const monthlyTransactions = this.transactions.filter(t => {
            const transactionDate = new Date(t.date);
            return transactionDate.getMonth() === currentMonth && 
                   transactionDate.getFullYear() === currentYear;
        });
        
        // Calculate totals
        const totalIncome = monthlyTransactions
            .filter(t => t.type === 'income')
            .reduce((sum, t) => sum + t.amount, 0);
            
        const totalExpense = monthlyTransactions
            .filter(t => t.type === 'expense')
            .reduce((sum, t) => sum + t.amount, 0);
            
        const netProfit = totalIncome - totalExpense;
        
        // Update dashboard stats
        document.getElementById('totalIncome').textContent = this.formatCurrency(totalIncome);
        document.getElementById('totalExpense').textContent = this.formatCurrency(totalExpense);
        document.getElementById('netProfit').textContent = this.formatCurrency(netProfit);
        document.getElementById('transactionCount').textContent = monthlyTransactions.length;
        
        // Update recent transactions
        this.updateRecentTransactions();
        this.updateAllTransactions();
    }
    
    updateRecentTransactions() {
        const container = document.getElementById('recentTransactions');
        const recentTransactions = this.transactions
            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
            .slice(0, 5);
            
        if (recentTransactions.length === 0) {
            container.innerHTML = `
                <p style="text-align: center; color: #666; padding: 2rem;">
                    Chưa có giao dịch nào. Hãy thêm giao dịch đầu tiên!
                </p>
            `;
            return;
        }
        
        container.innerHTML = recentTransactions.map(transaction => `
            <div class="transaction-item">
                <div>
                    <div style="font-weight: 500;">${transaction.description}</div>
                    <div style="font-size: 0.9rem; color: #666;">
                        ${transaction.category} • ${this.formatDate(transaction.date)}
                    </div>
                </div>
                <div class="${transaction.type === 'income' ? 'amount-positive' : 'amount-negative'}">
                    ${transaction.type === 'income' ? '+' : '-'}${this.formatCurrency(transaction.amount)}
                </div>
            </div>
        `).join('');
    }
    
    updateAllTransactions() {
        const container = document.getElementById('allTransactions');
        const allTransactions = this.transactions
            .sort((a, b) => new Date(b.date) - new Date(a.date));
            
        if (allTransactions.length === 0) {
            container.innerHTML = `
                <p style="text-align: center; color: #666; padding: 2rem;">
                    Chưa có giao dịch nào
                </p>
            `;
            return;
        }
        
        container.innerHTML = allTransactions.map(transaction => `
            <div class="transaction-item">
                <div>
                    <div style="font-weight: 500;">${transaction.description}</div>
                    <div style="font-size: 0.9rem; color: #666;">
                        ${transaction.category} • ${this.formatDate(transaction.date)}
                        ${transaction.notes ? ` • ${transaction.notes}` : ''}
                    </div>
                </div>
                <div class="${transaction.type === 'income' ? 'amount-positive' : 'amount-negative'}">
                    ${transaction.type === 'income' ? '+' : '-'}${this.formatCurrency(transaction.amount)}
                </div>
            </div>
        `).join('');
    }
    
    formatCurrency(amount) {
        return new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(amount);
    }
    
    formatDate(dateString) {
        return new Date(dateString).toLocaleDateString('vi-VN');
    }
}

// Navigation functions
function showSection(sectionId) {
    // Hide all sections
    document.querySelectorAll('.section').forEach(section => {
        section.classList.add('hidden');
    });
    
    // Show selected section
    document.getElementById(sectionId).classList.remove('hidden');
    
    // Update navigation
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });
    
    event.target.closest('.nav-link').classList.add('active');
}

// Tax calculation function
function calculateVAT() {
    const revenue = parseFloat(document.getElementById('revenue').value) || 0;
    const vatRate = parseFloat(document.getElementById('vatRate').value) || 0;
    
    if (revenue <= 0) {
        alert('Vui lòng nhập doanh thu hợp lệ!');
        return;
    }
    
    const vatAmount = revenue * (vatRate / 100);
    const totalAmount = revenue + vatAmount;
    
    const resultContainer = document.getElementById('vatResult');
    resultContainer.innerHTML = `
        <div style="background: #f8f9fa; padding: 1rem; border-radius: 8px; border-left: 4px solid #3498db;">
            <h4>Kết quả tính thuế GTGT:</h4>
            <p><strong>Doanh thu chưa thuế:</strong> ${app.formatCurrency(revenue)}</p>
            <p><strong>Thuế GTGT (${vatRate}%):</strong> ${app.formatCurrency(vatAmount)}</p>
            <p><strong>Tổng tiền phải thu:</strong> ${app.formatCurrency(totalAmount)}</p>
            <hr style="margin: 1rem 0;">
            <p><em>Công thức: Doanh thu × ${vatRate}% = ${app.formatCurrency(vatAmount)}</em></p>
        </div>
    `;
}

// Initialize app when page loads
let app;
document.addEventListener('DOMContentLoaded', () => {
    app = new AccountingApp();
    
    // Show welcome message for first-time users
    if (app.transactions.length === 0) {
        setTimeout(() => {
            alert('Chào mừng bạn đến với App Kế toán TT 133!\n\nĐây là phiên bản demo chạy hoàn toàn trên trình duyệt.\nDữ liệu sẽ được lưu trữ local trên máy tính của bạn.\n\nHãy bắt đầu bằng cách thêm giao dịch đầu tiên!');
        }, 1000);
    }
});
