const { Prisma } = require('@prisma/client');

/**
 * Global error handling middleware
 */
const errorHandler = (err, req, res, next) => {
  console.error('Error:', err);

  // Default error response
  let error = {
    success: false,
    message: 'Internal server error',
    statusCode: 500
  };

  // Prisma errors
  if (err instanceof Prisma.PrismaClientKnownRequestError) {
    switch (err.code) {
      case 'P2002':
        error.statusCode = 400;
        error.message = 'Duplicate entry. This record already exists.';
        error.field = err.meta?.target;
        break;
      case 'P2025':
        error.statusCode = 404;
        error.message = 'Record not found.';
        break;
      case 'P2003':
        error.statusCode = 400;
        error.message = 'Foreign key constraint failed.';
        break;
      case 'P2014':
        error.statusCode = 400;
        error.message = 'Invalid ID provided.';
        break;
      default:
        error.statusCode = 400;
        error.message = 'Database operation failed.';
    }
  }

  // Prisma validation errors
  if (err instanceof Prisma.PrismaClientValidationError) {
    error.statusCode = 400;
    error.message = 'Invalid data provided.';
  }

  // JWT errors
  if (err.name === 'JsonWebTokenError') {
    error.statusCode = 401;
    error.message = 'Invalid token.';
  }

  if (err.name === 'TokenExpiredError') {
    error.statusCode = 401;
    error.message = 'Token expired.';
  }

  // Validation errors (from express-validator or joi)
  if (err.name === 'ValidationError') {
    error.statusCode = 400;
    error.message = 'Validation failed.';
    error.details = err.details || err.errors;
  }

  // Multer errors (file upload)
  if (err.code === 'LIMIT_FILE_SIZE') {
    error.statusCode = 400;
    error.message = 'File too large.';
  }

  if (err.code === 'LIMIT_UNEXPECTED_FILE') {
    error.statusCode = 400;
    error.message = 'Unexpected file field.';
  }

  // Custom application errors
  if (err.statusCode) {
    error.statusCode = err.statusCode;
    error.message = err.message;
  }

  // Google API errors
  if (err.code >= 400 && err.code < 500 && err.response) {
    error.statusCode = err.code;
    error.message = 'Google API error: ' + (err.response.data?.error?.message || err.message);
  }

  // Rate limiting errors
  if (err.status === 429) {
    error.statusCode = 429;
    error.message = 'Too many requests. Please try again later.';
  }

  // Don't leak error details in production
  if (process.env.NODE_ENV === 'production') {
    delete error.stack;
    
    // Only show generic message for 500 errors in production
    if (error.statusCode === 500) {
      error.message = 'Something went wrong. Please try again later.';
    }
  } else {
    // Include stack trace in development
    error.stack = err.stack;
  }

  res.status(error.statusCode).json(error);
};

/**
 * Handle async errors in route handlers
 */
const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * Create custom error
 */
class AppError extends Error {
  constructor(message, statusCode = 500) {
    super(message);
    this.statusCode = statusCode;
    this.name = 'AppError';
    
    Error.captureStackTrace(this, this.constructor);
  }
}

module.exports = {
  errorHandler,
  asyncHandler,
  AppError
};
