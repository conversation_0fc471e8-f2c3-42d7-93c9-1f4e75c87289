import React from 'react';
import { Box, Typo<PERSON>, <PERSON><PERSON>, Card, CardContent } from '@mui/material';
import { Add as AddIcon } from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

const Transactions = () => {
  const navigate = useNavigate();

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1" sx={{ fontWeight: 600 }}>
          Quản lý giao dịch
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => navigate('/transactions/new')}
        >
          Thêm giao dịch
        </Button>
      </Box>

      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Danh sách giao dịch
          </Typography>
          <Typography color="text.secondary">
            T<PERSON>h năng này đang được phát triển...
          </Typography>
        </CardContent>
      </Card>
    </Box>
  );
};

export default Transactions;
