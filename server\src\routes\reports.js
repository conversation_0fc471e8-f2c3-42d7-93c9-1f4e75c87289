const express = require('express');
const { query, validationResult } = require('express-validator');
const { PrismaClient } = require('@prisma/client');
const { asyncHandler, AppError } = require('../middleware/errorHandler');

const router = express.Router();
const prisma = new PrismaClient();

/**
 * @swagger
 * /api/reports/income-statement:
 *   get:
 *     summary: Generate income statement (Báo cáo kết quả kinh doanh)
 *     tags: [Reports]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: startDate
 *         required: true
 *         schema:
 *           type: string
 *           format: date
 *       - in: query
 *         name: endDate
 *         required: true
 *         schema:
 *           type: string
 *           format: date
 *     responses:
 *       200:
 *         description: Income statement generated successfully
 */
router.get('/income-statement', [
  query('startDate').isISO8601(),
  query('endDate').isISO8601()
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new AppError('Validation failed', 400);
  }

  const { startDate, endDate } = req.query;

  // Get revenue transactions
  const revenueTransactions = await prisma.transaction.findMany({
    where: {
      userId: req.user.id,
      type: 'INCOME',
      date: {
        gte: new Date(startDate),
        lte: new Date(endDate)
      }
    },
    include: {
      category: true
    }
  });

  // Get expense transactions
  const expenseTransactions = await prisma.transaction.findMany({
    where: {
      userId: req.user.id,
      type: 'EXPENSE',
      date: {
        gte: new Date(startDate),
        lte: new Date(endDate)
      }
    },
    include: {
      category: true
    }
  });

  // Calculate totals
  const totalRevenue = revenueTransactions.reduce((sum, t) => sum + parseFloat(t.amount), 0);
  const totalExpenses = expenseTransactions.reduce((sum, t) => sum + parseFloat(t.amount), 0);
  const netIncome = totalRevenue - totalExpenses;

  // Group by categories
  const revenueByCategory = {};
  const expensesByCategory = {};

  revenueTransactions.forEach(t => {
    const categoryName = t.category.name;
    if (!revenueByCategory[categoryName]) {
      revenueByCategory[categoryName] = 0;
    }
    revenueByCategory[categoryName] += parseFloat(t.amount);
  });

  expenseTransactions.forEach(t => {
    const categoryName = t.category.name;
    if (!expensesByCategory[categoryName]) {
      expensesByCategory[categoryName] = 0;
    }
    expensesByCategory[categoryName] += parseFloat(t.amount);
  });

  res.json({
    success: true,
    data: {
      period: {
        startDate,
        endDate
      },
      revenue: {
        total: totalRevenue,
        byCategory: revenueByCategory
      },
      expenses: {
        total: totalExpenses,
        byCategory: expensesByCategory
      },
      netIncome,
      profitMargin: totalRevenue > 0 ? (netIncome / totalRevenue * 100) : 0
    }
  });
}));

/**
 * @swagger
 * /api/reports/cash-flow:
 *   get:
 *     summary: Generate cash flow statement
 *     tags: [Reports]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: startDate
 *         required: true
 *         schema:
 *           type: string
 *           format: date
 *       - in: query
 *         name: endDate
 *         required: true
 *         schema:
 *           type: string
 *           format: date
 *     responses:
 *       200:
 *         description: Cash flow statement generated successfully
 */
router.get('/cash-flow', [
  query('startDate').isISO8601(),
  query('endDate').isISO8601()
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new AppError('Validation failed', 400);
  }

  const { startDate, endDate } = req.query;

  // Get all transactions in the period
  const transactions = await prisma.transaction.findMany({
    where: {
      userId: req.user.id,
      date: {
        gte: new Date(startDate),
        lte: new Date(endDate)
      }
    },
    include: {
      category: true
    },
    orderBy: {
      date: 'asc'
    }
  });

  // Calculate opening balance (transactions before start date)
  const openingTransactions = await prisma.transaction.aggregate({
    where: {
      userId: req.user.id,
      date: {
        lt: new Date(startDate)
      }
    },
    _sum: {
      amount: true
    }
  });

  const openingIncome = await prisma.transaction.aggregate({
    where: {
      userId: req.user.id,
      type: 'INCOME',
      date: {
        lt: new Date(startDate)
      }
    },
    _sum: {
      amount: true
    }
  });

  const openingExpenses = await prisma.transaction.aggregate({
    where: {
      userId: req.user.id,
      type: 'EXPENSE',
      date: {
        lt: new Date(startDate)
      }
    },
    _sum: {
      amount: true
    }
  });

  const openingBalance = (openingIncome._sum.amount || 0) - (openingExpenses._sum.amount || 0);

  // Calculate period totals
  const periodIncome = transactions
    .filter(t => t.type === 'INCOME')
    .reduce((sum, t) => sum + parseFloat(t.amount), 0);

  const periodExpenses = transactions
    .filter(t => t.type === 'EXPENSE')
    .reduce((sum, t) => sum + parseFloat(t.amount), 0);

  const netCashFlow = periodIncome - periodExpenses;
  const closingBalance = openingBalance + netCashFlow;

  // Daily cash flow
  const dailyCashFlow = {};
  transactions.forEach(t => {
    const date = t.date.toISOString().split('T')[0];
    if (!dailyCashFlow[date]) {
      dailyCashFlow[date] = { income: 0, expenses: 0, net: 0 };
    }
    
    if (t.type === 'INCOME') {
      dailyCashFlow[date].income += parseFloat(t.amount);
    } else {
      dailyCashFlow[date].expenses += parseFloat(t.amount);
    }
    
    dailyCashFlow[date].net = dailyCashFlow[date].income - dailyCashFlow[date].expenses;
  });

  res.json({
    success: true,
    data: {
      period: {
        startDate,
        endDate
      },
      openingBalance,
      periodActivity: {
        income: periodIncome,
        expenses: periodExpenses,
        netCashFlow
      },
      closingBalance,
      dailyCashFlow
    }
  });
}));

/**
 * @swagger
 * /api/reports/tax-summary:
 *   get:
 *     summary: Generate tax summary report
 *     tags: [Reports]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: period
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: taxType
 *         schema:
 *           type: string
 *           enum: [VAT, CIT, PIT, LICENSE, RESOURCE, ENVIRONMENTAL, OTHER]
 *     responses:
 *       200:
 *         description: Tax summary generated successfully
 */
router.get('/tax-summary', [
  query('period').isString().notEmpty(),
  query('taxType').optional().isIn(['VAT', 'CIT', 'PIT', 'LICENSE', 'RESOURCE', 'ENVIRONMENTAL', 'OTHER'])
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new AppError('Validation failed', 400);
  }

  const { period, taxType } = req.query;

  // Get tax obligations for the period
  const where = {
    userId: req.user.id,
    period
  };
  if (taxType) where.taxType = taxType;

  const taxObligations = await prisma.taxObligation.findMany({
    where,
    orderBy: {
      taxType: 'asc'
    }
  });

  // Calculate summary by tax type
  const summary = {};
  taxObligations.forEach(obligation => {
    if (!summary[obligation.taxType]) {
      summary[obligation.taxType] = {
        baseAmount: 0,
        taxAmount: 0,
        status: {
          PENDING: 0,
          PAID: 0,
          OVERDUE: 0,
          WAIVED: 0
        }
      };
    }

    summary[obligation.taxType].baseAmount += parseFloat(obligation.baseAmount);
    summary[obligation.taxType].taxAmount += parseFloat(obligation.taxAmount);
    summary[obligation.taxType].status[obligation.status]++;
  });

  // Calculate totals
  const totals = {
    totalBaseAmount: 0,
    totalTaxAmount: 0,
    totalObligations: taxObligations.length,
    pendingAmount: 0,
    overdueAmount: 0
  };

  taxObligations.forEach(obligation => {
    totals.totalBaseAmount += parseFloat(obligation.baseAmount);
    totals.totalTaxAmount += parseFloat(obligation.taxAmount);
    
    if (obligation.status === 'PENDING') {
      totals.pendingAmount += parseFloat(obligation.taxAmount);
      
      if (new Date(obligation.dueDate) < new Date()) {
        totals.overdueAmount += parseFloat(obligation.taxAmount);
      }
    }
  });

  res.json({
    success: true,
    data: {
      period,
      taxType: taxType || 'ALL',
      summary,
      totals,
      obligations: taxObligations
    }
  });
}));

/**
 * @swagger
 * /api/reports/dashboard:
 *   get:
 *     summary: Get dashboard summary data
 *     tags: [Reports]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [week, month, quarter, year]
 *           default: month
 *     responses:
 *       200:
 *         description: Dashboard data retrieved successfully
 */
router.get('/dashboard', [
  query('period').optional().isIn(['week', 'month', 'quarter', 'year'])
], asyncHandler(async (req, res) => {
  const { period = 'month' } = req.query;

  // Calculate date range
  const now = new Date();
  let startDate;

  switch (period) {
    case 'week':
      startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 7);
      break;
    case 'quarter':
      startDate = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3, 1);
      break;
    case 'year':
      startDate = new Date(now.getFullYear(), 0, 1);
      break;
    default: // month
      startDate = new Date(now.getFullYear(), now.getMonth(), 1);
  }

  // Get financial summary
  const [incomeStats, expenseStats, upcomingTaxes, recentTransactions] = await Promise.all([
    prisma.transaction.aggregate({
      where: {
        userId: req.user.id,
        type: 'INCOME',
        date: { gte: startDate, lte: now }
      },
      _sum: { amount: true },
      _count: true
    }),
    prisma.transaction.aggregate({
      where: {
        userId: req.user.id,
        type: 'EXPENSE',
        date: { gte: startDate, lte: now }
      },
      _sum: { amount: true },
      _count: true
    }),
    prisma.taxObligation.findMany({
      where: {
        userId: req.user.id,
        status: 'PENDING',
        dueDate: {
          gte: now,
          lte: new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000) // Next 30 days
        }
      },
      orderBy: { dueDate: 'asc' },
      take: 5
    }),
    prisma.transaction.findMany({
      where: { userId: req.user.id },
      include: {
        category: {
          select: { name: true, type: true }
        }
      },
      orderBy: { date: 'desc' },
      take: 10
    })
  ]);

  const totalIncome = incomeStats._sum.amount || 0;
  const totalExpenses = expenseStats._sum.amount || 0;
  const netIncome = totalIncome - totalExpenses;

  // Get category breakdown
  const categoryBreakdown = await prisma.transaction.groupBy({
    by: ['categoryId'],
    where: {
      userId: req.user.id,
      date: { gte: startDate, lte: now }
    },
    _sum: { amount: true },
    _count: true
  });

  // Get category details
  const categoryIds = categoryBreakdown.map(cb => cb.categoryId);
  const categories = await prisma.category.findMany({
    where: { id: { in: categoryIds } },
    select: { id: true, name: true, type: true }
  });

  const categoryMap = {};
  categories.forEach(cat => {
    categoryMap[cat.id] = cat;
  });

  const enrichedCategoryBreakdown = categoryBreakdown.map(cb => ({
    ...cb,
    category: categoryMap[cb.categoryId]
  }));

  res.json({
    success: true,
    data: {
      period,
      dateRange: {
        startDate: startDate.toISOString(),
        endDate: now.toISOString()
      },
      summary: {
        totalIncome,
        totalExpenses,
        netIncome,
        transactionCount: incomeStats._count + expenseStats._count,
        profitMargin: totalIncome > 0 ? (netIncome / totalIncome * 100) : 0
      },
      categoryBreakdown: enrichedCategoryBreakdown,
      upcomingTaxes,
      recentTransactions
    }
  });
}));

module.exports = router;
