import React from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  LinearProgress,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  AccountBalance as AccountBalanceIcon,
  Receipt as ReceiptIcon,
  Warning as WarningIcon,
  Add as AddIcon,
} from '@mui/icons-material';
import { useQuery } from 'react-query';
import { useNavigate } from 'react-router-dom';

import { transactionsAPI, reportsAPI, formatCurrency } from '../../services/api';

const Dashboard = () => {
  const navigate = useNavigate();

  // Fetch dashboard data
  const { data: dashboardStats, isLoading: statsLoading } = useQuery(
    'dashboardStats',
    () => transactionsAPI.getDashboardStats({ period: 'month' }),
    {
      refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
    }
  );

  const { data: dashboardData, isLoading: dataLoading } = useQuery(
    'dashboardData',
    () => reportsAPI.getDashboard({ period: 'month' }),
    {
      refetchInterval: 5 * 60 * 1000,
    }
  );

  const isLoading = statsLoading || dataLoading;

  // Mock data for demonstration
  const mockStats = {
    current: {
      income: ********,
      expense: ********,
      net: 20000000,
      transactionCount: 45,
    },
    changes: {
      income: 15.5,
      expense: -8.2,
      net: 25.3,
    },
  };

  const mockUpcomingTaxes = [
    {
      id: 1,
      taxType: 'VAT',
      dueDate: '2024-01-20',
      amount: 5000000,
      status: 'PENDING',
    },
    {
      id: 2,
      taxType: 'CIT',
      dueDate: '2024-01-25',
      amount: 8000000,
      status: 'PENDING',
    },
  ];

  const mockRecentTransactions = [
    {
      id: 1,
      description: 'Doanh thu tư vấn tháng 12',
      amount: 15000000,
      type: 'INCOME',
      date: '2024-01-15',
      category: { name: 'Dịch vụ tư vấn' },
    },
    {
      id: 2,
      description: 'Chi phí văn phòng phẩm',
      amount: 2500000,
      type: 'EXPENSE',
      date: '2024-01-14',
      category: { name: 'Văn phòng phẩm' },
    },
  ];

  const stats = dashboardStats?.data || mockStats;
  const upcomingTaxes = dashboardData?.data?.upcomingTaxes || mockUpcomingTaxes;
  const recentTransactions = dashboardData?.data?.recentTransactions || mockRecentTransactions;

  const StatCard = ({ title, value, change, icon, color = 'primary' }) => (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box>
            <Typography color="text.secondary" gutterBottom variant="overline">
              {title}
            </Typography>
            <Typography variant="h4" component="div" sx={{ fontWeight: 600 }}>
              {formatCurrency(value)}
            </Typography>
            {change !== undefined && (
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                {change >= 0 ? (
                  <TrendingUpIcon sx={{ color: 'success.main', fontSize: 16, mr: 0.5 }} />
                ) : (
                  <TrendingDownIcon sx={{ color: 'error.main', fontSize: 16, mr: 0.5 }} />
                )}
                <Typography
                  variant="body2"
                  sx={{
                    color: change >= 0 ? 'success.main' : 'error.main',
                    fontWeight: 500,
                  }}
                >
                  {Math.abs(change).toFixed(1)}%
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ ml: 0.5 }}>
                  so với tháng trước
                </Typography>
              </Box>
            )}
          </Box>
          <Box
            sx={{
              width: 60,
              height: 60,
              borderRadius: 2,
              backgroundColor: `${color}.main`,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            {icon}
          </Box>
        </Box>
      </CardContent>
    </Card>
  );

  if (isLoading) {
    return (
      <Box>
        <Typography variant="h4" gutterBottom>
          Dashboard
        </Typography>
        <LinearProgress />
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1" sx={{ fontWeight: 600 }}>
          Dashboard
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => navigate('/transactions/new')}
        >
          Thêm giao dịch
        </Button>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Tổng thu"
            value={stats.current.income}
            change={stats.changes.income}
            icon={<TrendingUpIcon sx={{ color: 'white', fontSize: 28 }} />}
            color="success"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Tổng chi"
            value={stats.current.expense}
            change={stats.changes.expense}
            icon={<TrendingDownIcon sx={{ color: 'white', fontSize: 28 }} />}
            color="error"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Lợi nhuận ròng"
            value={stats.current.net}
            change={stats.changes.net}
            icon={<AccountBalanceIcon sx={{ color: 'white', fontSize: 28 }} />}
            color="primary"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Số giao dịch"
            value={stats.current.transactionCount}
            icon={<ReceiptIcon sx={{ color: 'white', fontSize: 28 }} />}
            color="info"
          />
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* Recent Transactions */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6" component="h2" sx={{ fontWeight: 600 }}>
                  Giao dịch gần đây
                </Typography>
                <Button
                  size="small"
                  onClick={() => navigate('/transactions')}
                >
                  Xem tất cả
                </Button>
              </Box>
              <List>
                {recentTransactions.map((transaction, index) => (
                  <ListItem
                    key={transaction.id}
                    divider={index < recentTransactions.length - 1}
                    sx={{ px: 0 }}
                  >
                    <ListItemIcon>
                      <Box
                        sx={{
                          width: 40,
                          height: 40,
                          borderRadius: 1,
                          backgroundColor: transaction.type === 'INCOME' ? 'success.light' : 'error.light',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                      >
                        {transaction.type === 'INCOME' ? (
                          <TrendingUpIcon sx={{ color: 'success.main', fontSize: 20 }} />
                        ) : (
                          <TrendingDownIcon sx={{ color: 'error.main', fontSize: 20 }} />
                        )}
                      </Box>
                    </ListItemIcon>
                    <ListItemText
                      primary={transaction.description}
                      secondary={`${transaction.category.name} • ${new Date(transaction.date).toLocaleDateString('vi-VN')}`}
                    />
                    <Typography
                      variant="body2"
                      sx={{
                        fontWeight: 600,
                        color: transaction.type === 'INCOME' ? 'success.main' : 'error.main',
                      }}
                    >
                      {transaction.type === 'INCOME' ? '+' : '-'}{formatCurrency(transaction.amount)}
                    </Typography>
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Upcoming Tax Obligations */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6" component="h2" sx={{ fontWeight: 600 }}>
                  Nghĩa vụ thuế sắp tới
                </Typography>
                <Button
                  size="small"
                  onClick={() => navigate('/tax')}
                >
                  Xem tất cả
                </Button>
              </Box>
              <List>
                {upcomingTaxes.map((tax, index) => (
                  <ListItem
                    key={tax.id}
                    divider={index < upcomingTaxes.length - 1}
                    sx={{ px: 0 }}
                  >
                    <ListItemIcon>
                      <WarningIcon color="warning" />
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography variant="body2">
                            Thuế {tax.taxType}
                          </Typography>
                          <Chip
                            label={tax.status === 'PENDING' ? 'Chưa nộp' : 'Đã nộp'}
                            size="small"
                            color={tax.status === 'PENDING' ? 'warning' : 'success'}
                          />
                        </Box>
                      }
                      secondary={`Hạn nộp: ${new Date(tax.dueDate).toLocaleDateString('vi-VN')}`}
                    />
                    <Typography variant="body2" sx={{ fontWeight: 600 }}>
                      {formatCurrency(tax.amount)}
                    </Typography>
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;
