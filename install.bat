@echo off
echo ========================================
echo   App Ke Toan TT 133 - Installation
echo ========================================
echo.

echo [1/5] Installing root dependencies...
call npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install root dependencies
    pause
    exit /b 1
)

echo.
echo [2/5] Installing client dependencies...
cd client
call npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install client dependencies
    pause
    exit /b 1
)

echo.
echo [3/5] Installing server dependencies...
cd ..\server
call npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install server dependencies
    pause
    exit /b 1
)

echo.
echo [4/5] Setting up environment file...
if not exist .env (
    copy .env.example .env
    echo Created .env file from .env.example
    echo Please edit .env file with your database configuration
) else (
    echo .env file already exists
)

echo.
echo [5/5] Generating Prisma client...
call npm run db:generate
if %errorlevel% neq 0 (
    echo ERROR: Failed to generate Prisma client
    echo Make sure PostgreSQL is installed and running
    pause
    exit /b 1
)

cd ..

echo.
echo ========================================
echo   Installation completed successfully!
echo ========================================
echo.
echo Next steps:
echo 1. Create PostgreSQL database: createdb app_ke_toan_133
echo 2. Edit server/.env with your database URL
echo 3. Run migrations: cd server && npm run db:migrate
echo 4. Seed initial data: npm run db:seed
echo 5. Start the application: npm run dev
echo.
echo For detailed instructions, see setup.md
echo.
pause
