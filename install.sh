#!/bin/bash

echo "========================================"
echo "  App Ke Toan TT 133 - Installation"
echo "========================================"
echo

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js 16+ first."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 16 ]; then
    print_error "Node.js version 16+ is required. Current version: $(node -v)"
    exit 1
fi

print_status "Node.js version: $(node -v) ✓"

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    print_error "npm is not installed. Please install npm first."
    exit 1
fi

print_status "npm version: $(npm -v) ✓"

echo
print_status "[1/5] Installing root dependencies..."
npm install
if [ $? -ne 0 ]; then
    print_error "Failed to install root dependencies"
    exit 1
fi

echo
print_status "[2/5] Installing client dependencies..."
cd client
npm install
if [ $? -ne 0 ]; then
    print_error "Failed to install client dependencies"
    exit 1
fi

echo
print_status "[3/5] Installing server dependencies..."
cd ../server
npm install
if [ $? -ne 0 ]; then
    print_error "Failed to install server dependencies"
    exit 1
fi

echo
print_status "[4/5] Setting up environment file..."
if [ ! -f .env ]; then
    cp .env.example .env
    print_status "Created .env file from .env.example"
    print_warning "Please edit .env file with your database configuration"
else
    print_warning ".env file already exists"
fi

echo
print_status "[5/5] Generating Prisma client..."
npm run db:generate
if [ $? -ne 0 ]; then
    print_error "Failed to generate Prisma client"
    print_error "Make sure PostgreSQL is installed and running"
    exit 1
fi

cd ..

echo
echo "========================================"
echo -e "${GREEN}  Installation completed successfully!${NC}"
echo "========================================"
echo
echo "Next steps:"
echo "1. Create PostgreSQL database: createdb app_ke_toan_133"
echo "2. Edit server/.env with your database URL"
echo "3. Run migrations: cd server && npm run db:migrate"
echo "4. Seed initial data: npm run db:seed"
echo "5. Start the application: npm run dev"
echo
echo "For detailed instructions, see setup.md"
echo

# Make the script executable
chmod +x install.sh
