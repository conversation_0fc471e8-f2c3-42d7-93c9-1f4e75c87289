const express = require('express');
const { body, validationResult } = require('express-validator');
const { google } = require('googleapis');
const { PrismaClient } = require('@prisma/client');
const { asyncHandler, AppError } = require('../middleware/errorHandler');

const router = express.Router();
const prisma = new PrismaClient();

// Google Sheets API setup
const SCOPES = ['https://www.googleapis.com/auth/spreadsheets'];

/**
 * @swagger
 * /api/google-sheets/auth-url:
 *   get:
 *     summary: Get Google OAuth2 authorization URL
 *     tags: [Google Sheets]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Authorization URL generated successfully
 */
router.get('/auth-url', asyncHandler(async (req, res) => {
  const oauth2Client = new google.auth.OAuth2(
    process.env.GOOGLE_CLIENT_ID,
    process.env.GOOGLE_CLIENT_SECRET,
    process.env.GOOGLE_REDIRECT_URI
  );

  const authUrl = oauth2Client.generateAuthUrl({
    access_type: 'offline',
    scope: SCOPES,
    state: req.user.id // Pass user ID in state for security
  });

  res.json({
    success: true,
    data: { authUrl }
  });
}));

/**
 * @swagger
 * /api/google-sheets/callback:
 *   post:
 *     summary: Handle Google OAuth2 callback
 *     tags: [Google Sheets]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - code
 *             properties:
 *               code:
 *                 type: string
 *     responses:
 *       200:
 *         description: Google Sheets connected successfully
 */
router.post('/callback', [
  body('code').isString().notEmpty()
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new AppError('Validation failed', 400);
  }

  const { code } = req.body;

  const oauth2Client = new google.auth.OAuth2(
    process.env.GOOGLE_CLIENT_ID,
    process.env.GOOGLE_CLIENT_SECRET,
    process.env.GOOGLE_REDIRECT_URI
  );

  try {
    const { tokens } = await oauth2Client.getToken(code);
    oauth2Client.setCredentials(tokens);

    // Create a new spreadsheet for the user
    const sheets = google.sheets({ version: 'v4', auth: oauth2Client });
    
    const spreadsheet = await sheets.spreadsheets.create({
      resource: {
        properties: {
          title: `Kế toán TT133 - ${req.user.name || req.user.email}`
        },
        sheets: [
          {
            properties: {
              title: 'Thu nhập',
              gridProperties: {
                rowCount: 1000,
                columnCount: 10
              }
            }
          },
          {
            properties: {
              title: 'Chi phí',
              gridProperties: {
                rowCount: 1000,
                columnCount: 10
              }
            }
          },
          {
            properties: {
              title: 'Tổng hợp',
              gridProperties: {
                rowCount: 100,
                columnCount: 10
              }
            }
          }
        ]
      }
    });

    const spreadsheetId = spreadsheet.data.spreadsheetId;

    // Set up headers for each sheet
    await setupSheetHeaders(sheets, spreadsheetId);

    // Store Google Sheets info in user settings
    await prisma.user.update({
      where: { id: req.user.id },
      data: {
        googleSheetId: spreadsheetId,
        settings: {
          ...req.user.settings,
          googleSheets: {
            connected: true,
            spreadsheetId,
            accessToken: tokens.access_token,
            refreshToken: tokens.refresh_token,
            connectedAt: new Date().toISOString()
          }
        }
      }
    });

    res.json({
      success: true,
      message: 'Google Sheets connected successfully',
      data: {
        spreadsheetId,
        spreadsheetUrl: `https://docs.google.com/spreadsheets/d/${spreadsheetId}`
      }
    });
  } catch (error) {
    console.error('Google Sheets callback error:', error);
    throw new AppError('Failed to connect Google Sheets', 500);
  }
}));

/**
 * @swagger
 * /api/google-sheets/sync:
 *   post:
 *     summary: Sync transactions to Google Sheets
 *     tags: [Google Sheets]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               startDate:
 *                 type: string
 *                 format: date
 *               endDate:
 *                 type: string
 *                 format: date
 *               fullSync:
 *                 type: boolean
 *                 default: false
 *     responses:
 *       200:
 *         description: Sync completed successfully
 */
router.post('/sync', [
  body('startDate').optional().isISO8601(),
  body('endDate').optional().isISO8601(),
  body('fullSync').optional().isBoolean()
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new AppError('Validation failed', 400);
  }

  const { startDate, endDate, fullSync = false } = req.body;

  // Check if user has Google Sheets connected
  const user = await prisma.user.findUnique({
    where: { id: req.user.id }
  });

  if (!user.googleSheetId || !user.settings?.googleSheets?.connected) {
    throw new AppError('Google Sheets not connected', 400);
  }

  const googleSheets = user.settings.googleSheets;
  
  // Set up OAuth2 client
  const oauth2Client = new google.auth.OAuth2(
    process.env.GOOGLE_CLIENT_ID,
    process.env.GOOGLE_CLIENT_SECRET,
    process.env.GOOGLE_REDIRECT_URI
  );

  oauth2Client.setCredentials({
    access_token: googleSheets.accessToken,
    refresh_token: googleSheets.refreshToken
  });

  const sheets = google.sheets({ version: 'v4', auth: oauth2Client });

  try {
    // Build date filter
    const dateFilter = {};
    if (!fullSync) {
      if (startDate) dateFilter.gte = new Date(startDate);
      if (endDate) dateFilter.lte = new Date(endDate);
      
      // Default to last 30 days if no dates provided
      if (!startDate && !endDate) {
        dateFilter.gte = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      }
    }

    // Get transactions to sync
    const where = { userId: req.user.id };
    if (Object.keys(dateFilter).length > 0) {
      where.date = dateFilter;
    }

    const transactions = await prisma.transaction.findMany({
      where,
      include: {
        category: {
          select: { name: true }
        }
      },
      orderBy: { date: 'asc' }
    });

    // Separate income and expense transactions
    const incomeTransactions = transactions.filter(t => t.type === 'INCOME');
    const expenseTransactions = transactions.filter(t => t.type === 'EXPENSE');

    // Clear existing data if full sync
    if (fullSync) {
      await clearSheetData(sheets, user.googleSheetId, 'Thu nhập');
      await clearSheetData(sheets, user.googleSheetId, 'Chi phí');
    }

    // Sync income transactions
    if (incomeTransactions.length > 0) {
      const incomeData = incomeTransactions.map(t => [
        t.date.toLocaleDateString('vi-VN'),
        t.description,
        t.category.name,
        t.amount.toString(),
        t.notes || '',
        t.reference || '',
        t.formulaDetails ? JSON.stringify(t.formulaDetails) : ''
      ]);

      await appendToSheet(sheets, user.googleSheetId, 'Thu nhập', incomeData);
    }

    // Sync expense transactions
    if (expenseTransactions.length > 0) {
      const expenseData = expenseTransactions.map(t => [
        t.date.toLocaleDateString('vi-VN'),
        t.description,
        t.category.name,
        t.amount.toString(),
        t.notes || '',
        t.reference || '',
        t.formulaDetails ? JSON.stringify(t.formulaDetails) : ''
      ]);

      await appendToSheet(sheets, user.googleSheetId, 'Chi phí', expenseData);
    }

    // Update summary sheet
    await updateSummarySheet(sheets, user.googleSheetId, req.user.id);

    res.json({
      success: true,
      message: 'Sync completed successfully',
      data: {
        syncedTransactions: transactions.length,
        incomeTransactions: incomeTransactions.length,
        expenseTransactions: expenseTransactions.length,
        spreadsheetUrl: `https://docs.google.com/spreadsheets/d/${user.googleSheetId}`
      }
    });
  } catch (error) {
    console.error('Google Sheets sync error:', error);
    throw new AppError('Failed to sync with Google Sheets', 500);
  }
}));

/**
 * @swagger
 * /api/google-sheets/disconnect:
 *   post:
 *     summary: Disconnect Google Sheets
 *     tags: [Google Sheets]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Google Sheets disconnected successfully
 */
router.post('/disconnect', asyncHandler(async (req, res) => {
  await prisma.user.update({
    where: { id: req.user.id },
    data: {
      googleSheetId: null,
      settings: {
        ...req.user.settings,
        googleSheets: {
          connected: false,
          disconnectedAt: new Date().toISOString()
        }
      }
    }
  });

  res.json({
    success: true,
    message: 'Google Sheets disconnected successfully'
  });
}));

// Helper functions
async function setupSheetHeaders(sheets, spreadsheetId) {
  const headers = [
    'Ngày',
    'Mô tả',
    'Danh mục',
    'Số tiền',
    'Ghi chú',
    'Số tham chiếu',
    'Chi tiết công thức'
  ];

  // Set headers for income sheet
  await sheets.spreadsheets.values.update({
    spreadsheetId,
    range: 'Thu nhập!A1:G1',
    valueInputOption: 'RAW',
    resource: {
      values: [headers]
    }
  });

  // Set headers for expense sheet
  await sheets.spreadsheets.values.update({
    spreadsheetId,
    range: 'Chi phí!A1:G1',
    valueInputOption: 'RAW',
    resource: {
      values: [headers]
    }
  });

  // Set headers for summary sheet
  const summaryHeaders = [
    'Kỳ',
    'Tổng thu',
    'Tổng chi',
    'Lợi nhuận ròng',
    'Tỷ lệ lợi nhuận (%)'
  ];

  await sheets.spreadsheets.values.update({
    spreadsheetId,
    range: 'Tổng hợp!A1:E1',
    valueInputOption: 'RAW',
    resource: {
      values: [summaryHeaders]
    }
  });
}

async function clearSheetData(sheets, spreadsheetId, sheetName) {
  await sheets.spreadsheets.values.clear({
    spreadsheetId,
    range: `${sheetName}!A2:G1000`
  });
}

async function appendToSheet(sheets, spreadsheetId, sheetName, data) {
  await sheets.spreadsheets.values.append({
    spreadsheetId,
    range: `${sheetName}!A:G`,
    valueInputOption: 'RAW',
    resource: {
      values: data
    }
  });
}

async function updateSummarySheet(sheets, spreadsheetId, userId) {
  // Get monthly summary data
  const now = new Date();
  const currentMonth = new Date(now.getFullYear(), now.getMonth(), 1);
  const nextMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1);

  const [incomeSum, expenseSum] = await Promise.all([
    prisma.transaction.aggregate({
      where: {
        userId,
        type: 'INCOME',
        date: { gte: currentMonth, lt: nextMonth }
      },
      _sum: { amount: true }
    }),
    prisma.transaction.aggregate({
      where: {
        userId,
        type: 'EXPENSE',
        date: { gte: currentMonth, lt: nextMonth }
      },
      _sum: { amount: true }
    })
  ]);

  const totalIncome = incomeSum._sum.amount || 0;
  const totalExpense = expenseSum._sum.amount || 0;
  const netProfit = totalIncome - totalExpense;
  const profitMargin = totalIncome > 0 ? (netProfit / totalIncome * 100) : 0;

  const summaryData = [
    `${now.getMonth() + 1}/${now.getFullYear()}`,
    totalIncome.toString(),
    totalExpense.toString(),
    netProfit.toString(),
    profitMargin.toFixed(2)
  ];

  await sheets.spreadsheets.values.append({
    spreadsheetId,
    range: 'Tổng hợp!A:E',
    valueInputOption: 'RAW',
    resource: {
      values: [summaryData]
    }
  });
}

module.exports = router;
