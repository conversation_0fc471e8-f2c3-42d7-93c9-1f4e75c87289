import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import toast from 'react-hot-toast';

import { authAPI } from '../services/api';

// Auth context
const AuthContext = createContext();

// Auth reducer
const authReducer = (state, action) => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    case 'SET_USER':
      return { ...state, user: action.payload, loading: false };
    case 'SET_TOKEN':
      return { ...state, token: action.payload };
    case 'LOGOUT':
      return { ...state, user: null, token: null, loading: false };
    default:
      return state;
  }
};

// Initial state
const initialState = {
  user: null,
  token: localStorage.getItem('token'),
  loading: true,
};

// Auth provider component
export const AuthProvider = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);
  const queryClient = useQueryClient();

  // Get current user query
  const { data: userData, isLoading } = useQuery(
    'currentUser',
    authAPI.getCurrentUser,
    {
      enabled: !!state.token,
      retry: false,
      onSuccess: (data) => {
        dispatch({ type: 'SET_USER', payload: data.user });
      },
      onError: () => {
        // Token is invalid, clear it
        localStorage.removeItem('token');
        dispatch({ type: 'LOGOUT' });
      },
    }
  );

  // Login mutation
  const loginMutation = useMutation(authAPI.login, {
    onSuccess: (data) => {
      const { user, token } = data;
      localStorage.setItem('token', token);
      dispatch({ type: 'SET_TOKEN', payload: token });
      dispatch({ type: 'SET_USER', payload: user });
      queryClient.setQueryData('currentUser', data);
      toast.success(`Chào mừng ${user.name || user.email}!`);
    },
    onError: (error) => {
      const message = error.response?.data?.message || 'Đăng nhập thất bại';
      toast.error(message);
    },
  });

  // Register mutation
  const registerMutation = useMutation(authAPI.register, {
    onSuccess: (data) => {
      const { user, token } = data;
      localStorage.setItem('token', token);
      dispatch({ type: 'SET_TOKEN', payload: token });
      dispatch({ type: 'SET_USER', payload: user });
      queryClient.setQueryData('currentUser', data);
      toast.success(`Đăng ký thành công! Chào mừng ${user.name}!`);
    },
    onError: (error) => {
      const message = error.response?.data?.message || 'Đăng ký thất bại';
      toast.error(message);
    },
  });

  // Update profile mutation
  const updateProfileMutation = useMutation(authAPI.updateProfile, {
    onSuccess: (data) => {
      dispatch({ type: 'SET_USER', payload: data.user });
      queryClient.setQueryData('currentUser', data);
      toast.success('Cập nhật thông tin thành công!');
    },
    onError: (error) => {
      const message = error.response?.data?.message || 'Cập nhật thất bại';
      toast.error(message);
    },
  });

  // Change password mutation
  const changePasswordMutation = useMutation(authAPI.changePassword, {
    onSuccess: () => {
      toast.success('Đổi mật khẩu thành công!');
    },
    onError: (error) => {
      const message = error.response?.data?.message || 'Đổi mật khẩu thất bại';
      toast.error(message);
    },
  });

  // Login function
  const login = async (credentials) => {
    return loginMutation.mutateAsync(credentials);
  };

  // Register function
  const register = async (userData) => {
    return registerMutation.mutateAsync(userData);
  };

  // Logout function
  const logout = () => {
    localStorage.removeItem('token');
    dispatch({ type: 'LOGOUT' });
    queryClient.clear();
    toast.success('Đăng xuất thành công!');
  };

  // Update profile function
  const updateProfile = async (profileData) => {
    return updateProfileMutation.mutateAsync(profileData);
  };

  // Change password function
  const changePassword = async (passwordData) => {
    return changePasswordMutation.mutateAsync(passwordData);
  };

  // Set loading to false when not fetching user data
  useEffect(() => {
    if (!state.token) {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  }, [state.token]);

  // Context value
  const value = {
    user: state.user,
    token: state.token,
    loading: state.loading || isLoading,
    login,
    register,
    logout,
    updateProfile,
    changePassword,
    isLoggingIn: loginMutation.isLoading,
    isRegistering: registerMutation.isLoading,
    isUpdatingProfile: updateProfileMutation.isLoading,
    isChangingPassword: changePasswordMutation.isLoading,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
