const express = require('express');
const { body, query, validationResult } = require('express-validator');
const { PrismaClient } = require('@prisma/client');
const { asyncHandler, AppError } = require('../middleware/errorHandler');

const router = express.Router();
const prisma = new PrismaClient();

/**
 * @swagger
 * /api/tax/config:
 *   get:
 *     summary: Get tax configuration
 *     tags: [Tax]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: taxType
 *         schema:
 *           type: string
 *           enum: [VAT, CIT, PIT, LICENSE, RESOURCE, ENVIRONMENTAL, OTHER]
 *     responses:
 *       200:
 *         description: Tax configuration retrieved successfully
 */
router.get('/config', [
  query('taxType').optional().isIn(['VAT', 'CIT', 'PIT', 'LICENSE', 'RESOURCE', 'ENVIRONMENTAL', 'OTHER'])
], asyncHand<PERSON>(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new AppError('Validation failed', 400);
  }

  const { taxType } = req.query;

  const where = { isActive: true };
  if (taxType) where.taxType = taxType;

  const taxConfigs = await prisma.taxConfig.findMany({
    where,
    orderBy: [
      { taxType: 'asc' },
      { rate: 'asc' }
    ]
  });

  res.json({
    success: true,
    data: { taxConfigs }
  });
}));

/**
 * @swagger
 * /api/tax/calculate:
 *   post:
 *     summary: Calculate tax for a transaction
 *     tags: [Tax]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - amount
 *               - taxType
 *               - transactionType
 *             properties:
 *               amount:
 *                 type: number
 *                 minimum: 0
 *               taxType:
 *                 type: string
 *                 enum: [VAT, CIT, PIT]
 *               transactionType:
 *                 type: string
 *                 enum: [INCOME, EXPENSE]
 *               serviceType:
 *                 type: string
 *                 description: For VAT calculation
 *               employeeCount:
 *                 type: integer
 *                 description: For PIT calculation
 *               dependents:
 *                 type: integer
 *                 description: For PIT calculation
 *     responses:
 *       200:
 *         description: Tax calculated successfully
 */
router.post('/calculate', [
  body('amount').isFloat({ min: 0 }),
  body('taxType').isIn(['VAT', 'CIT', 'PIT']),
  body('transactionType').isIn(['INCOME', 'EXPENSE']),
  body('serviceType').optional().isString(),
  body('employeeCount').optional().isInt({ min: 1 }),
  body('dependents').optional().isInt({ min: 0 })
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new AppError('Validation failed', 400);
  }

  const {
    amount,
    taxType,
    transactionType,
    serviceType,
    employeeCount = 1,
    dependents = 0
  } = req.body;

  let taxCalculation = {};

  switch (taxType) {
    case 'VAT':
      taxCalculation = calculateVAT(amount, transactionType, serviceType);
      break;
    case 'CIT':
      taxCalculation = calculateCIT(amount);
      break;
    case 'PIT':
      taxCalculation = calculatePIT(amount, employeeCount, dependents);
      break;
    default:
      throw new AppError('Unsupported tax type', 400);
  }

  res.json({
    success: true,
    data: {
      input: {
        amount,
        taxType,
        transactionType,
        serviceType,
        employeeCount,
        dependents
      },
      calculation: taxCalculation
    }
  });
}));

/**
 * @swagger
 * /api/tax/obligations:
 *   get:
 *     summary: Get tax obligations
 *     tags: [Tax]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *       - in: query
 *         name: taxType
 *         schema:
 *           type: string
 *           enum: [VAT, CIT, PIT, LICENSE, RESOURCE, ENVIRONMENTAL, OTHER]
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [PENDING, PAID, OVERDUE, WAIVED]
 *     responses:
 *       200:
 *         description: Tax obligations retrieved successfully
 */
router.get('/obligations', [
  query('taxType').optional().isIn(['VAT', 'CIT', 'PIT', 'LICENSE', 'RESOURCE', 'ENVIRONMENTAL', 'OTHER']),
  query('status').optional().isIn(['PENDING', 'PAID', 'OVERDUE', 'WAIVED'])
], asyncHandler(async (req, res) => {
  const { period, taxType, status } = req.query;

  const where = { userId: req.user.id };
  if (period) where.period = period;
  if (taxType) where.taxType = taxType;
  if (status) where.status = status;

  const obligations = await prisma.taxObligation.findMany({
    where,
    orderBy: [
      { dueDate: 'asc' },
      { taxType: 'asc' }
    ]
  });

  // Calculate summary
  const summary = {
    total: obligations.length,
    pending: obligations.filter(o => o.status === 'PENDING').length,
    overdue: obligations.filter(o => o.status === 'PENDING' && new Date(o.dueDate) < new Date()).length,
    totalAmount: obligations.reduce((sum, o) => sum + parseFloat(o.taxAmount), 0),
    pendingAmount: obligations
      .filter(o => o.status === 'PENDING')
      .reduce((sum, o) => sum + parseFloat(o.taxAmount), 0)
  };

  res.json({
    success: true,
    data: {
      obligations,
      summary
    }
  });
}));

/**
 * @swagger
 * /api/tax/obligations:
 *   post:
 *     summary: Create tax obligation
 *     tags: [Tax]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - period
 *               - taxType
 *               - baseAmount
 *               - taxAmount
 *               - dueDate
 *             properties:
 *               period:
 *                 type: string
 *               taxType:
 *                 type: string
 *                 enum: [VAT, CIT, PIT, LICENSE, RESOURCE, ENVIRONMENTAL, OTHER]
 *               baseAmount:
 *                 type: number
 *                 minimum: 0
 *               taxAmount:
 *                 type: number
 *                 minimum: 0
 *               dueDate:
 *                 type: string
 *                 format: date
 *     responses:
 *       201:
 *         description: Tax obligation created successfully
 */
router.post('/obligations', [
  body('period').isString().notEmpty(),
  body('taxType').isIn(['VAT', 'CIT', 'PIT', 'LICENSE', 'RESOURCE', 'ENVIRONMENTAL', 'OTHER']),
  body('baseAmount').isFloat({ min: 0 }),
  body('taxAmount').isFloat({ min: 0 }),
  body('dueDate').isISO8601()
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new AppError('Validation failed', 400);
  }

  const { period, taxType, baseAmount, taxAmount, dueDate } = req.body;

  // Check if obligation already exists for this period and tax type
  const existingObligation = await prisma.taxObligation.findFirst({
    where: {
      userId: req.user.id,
      period,
      taxType
    }
  });

  if (existingObligation) {
    throw new AppError('Tax obligation already exists for this period and tax type', 400);
  }

  const obligation = await prisma.taxObligation.create({
    data: {
      period,
      taxType,
      baseAmount,
      taxAmount,
      dueDate: new Date(dueDate),
      userId: req.user.id
    }
  });

  res.status(201).json({
    success: true,
    message: 'Tax obligation created successfully',
    data: { obligation }
  });
}));

// Helper functions for tax calculations
function calculateVAT(amount, transactionType, serviceType = 'general') {
  let rate = 0.10; // Default 10%

  // Determine VAT rate based on service type
  switch (serviceType) {
    case 'education':
    case 'healthcare':
    case 'export':
      rate = 0;
      break;
    case 'water':
    case 'medical':
      rate = 0.05;
      break;
    default:
      rate = 0.10;
  }

  let vatAmount = 0;
  let baseAmount = 0;

  if (transactionType === 'INCOME') {
    // VAT included in selling price
    baseAmount = amount / (1 + rate);
    vatAmount = amount - baseAmount;
  } else {
    // VAT on purchase (input VAT)
    baseAmount = amount / (1 + rate);
    vatAmount = amount - baseAmount;
  }

  return {
    rate: rate * 100,
    baseAmount: Math.round(baseAmount),
    vatAmount: Math.round(vatAmount),
    totalAmount: amount,
    formula: `Base: ${Math.round(baseAmount)} + VAT(${rate * 100}%): ${Math.round(vatAmount)} = ${amount}`,
    journalEntries: generateVATJournalEntries(baseAmount, vatAmount, transactionType)
  };
}

function calculateCIT(revenue) {
  const rate = 0.20; // 20% corporate income tax
  const taxAmount = revenue * rate;

  return {
    rate: rate * 100,
    taxableIncome: revenue,
    taxAmount: Math.round(taxAmount),
    formula: `Taxable Income: ${revenue} × ${rate * 100}% = ${Math.round(taxAmount)}`
  };
}

function calculatePIT(grossSalary, employeeCount = 1, dependents = 0) {
  const socialInsurance = grossSalary * 0.08; // 8%
  const healthInsurance = grossSalary * 0.015; // 1.5%
  const unemploymentInsurance = grossSalary * 0.01; // 1%
  const totalInsurance = socialInsurance + healthInsurance + unemploymentInsurance;

  const personalDeduction = 11000000; // 11 million VND
  const dependentDeduction = dependents * 4400000; // 4.4 million VND per dependent

  const taxableIncome = Math.max(0, grossSalary - totalInsurance - personalDeduction - dependentDeduction);

  // Progressive tax brackets
  let tax = 0;
  const brackets = [
    { from: 0, to: 5000000, rate: 0.05 },
    { from: 5000001, to: 10000000, rate: 0.10 },
    { from: 10000001, to: 18000000, rate: 0.15 },
    { from: 18000001, to: 32000000, rate: 0.20 },
    { from: 32000001, to: 52000000, rate: 0.25 },
    { from: 52000001, to: 80000000, rate: 0.30 },
    { from: 80000001, to: Infinity, rate: 0.35 }
  ];

  for (const bracket of brackets) {
    if (taxableIncome > bracket.from) {
      const taxableInBracket = Math.min(taxableIncome, bracket.to) - bracket.from + 1;
      tax += taxableInBracket * bracket.rate;
    }
  }

  const netSalary = grossSalary - totalInsurance - tax;

  return {
    grossSalary,
    insurance: {
      social: Math.round(socialInsurance),
      health: Math.round(healthInsurance),
      unemployment: Math.round(unemploymentInsurance),
      total: Math.round(totalInsurance)
    },
    deductions: {
      personal: personalDeduction,
      dependents: dependentDeduction,
      total: personalDeduction + dependentDeduction
    },
    taxableIncome: Math.round(taxableIncome),
    tax: Math.round(tax),
    netSalary: Math.round(netSalary),
    formula: `Gross: ${grossSalary} - Insurance: ${Math.round(totalInsurance)} - Tax: ${Math.round(tax)} = Net: ${Math.round(netSalary)}`
  };
}

function generateVATJournalEntries(baseAmount, vatAmount, transactionType) {
  if (transactionType === 'INCOME') {
    return [
      { account: '111', description: 'Tiền mặt', debit: baseAmount + vatAmount, credit: 0 },
      { account: '511', description: 'Doanh thu', debit: 0, credit: baseAmount },
      { account: '3331', description: 'Thuế GTGT phải nộp', debit: 0, credit: vatAmount }
    ];
  } else {
    return [
      { account: '621', description: 'Chi phí', debit: baseAmount, credit: 0 },
      { account: '1331', description: 'Thuế GTGT được khấu trừ', debit: vatAmount, credit: 0 },
      { account: '111', description: 'Tiền mặt', debit: 0, credit: baseAmount + vatAmount }
    ];
  }
}

module.exports = router;
