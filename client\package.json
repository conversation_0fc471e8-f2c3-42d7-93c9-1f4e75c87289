{"name": "app-ke-toan-client", "version": "1.0.0", "description": "Frontend cho ứng dụng kế toán TT 133", "private": true, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.19", "@mui/material": "^5.14.20", "@mui/x-charts": "^6.18.1", "@mui/x-date-pickers": "^6.18.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1", "axios": "^1.6.2", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "yup": "^1.3.3", "react-query": "^3.39.3", "react-hot-toast": "^2.4.1", "dayjs": "^1.11.10", "recharts": "^2.8.0", "react-beautiful-dnd": "^13.1.1", "react-csv": "^2.2.2", "jspdf": "^2.5.1", "html2canvas": "^1.4.1"}, "devDependencies": {"@types/react": "^18.2.39", "@types/react-dom": "^18.2.17", "typescript": "^4.9.5", "web-vitals": "^3.5.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5000"}