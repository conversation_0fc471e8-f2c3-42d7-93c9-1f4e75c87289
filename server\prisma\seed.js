const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Seed Chart of Accounts according to Circular 133/2016/TT-BTC
  console.log('📊 Seeding Chart of Accounts...');
  
  const accounts = [
    // ASSETS (Tài sản)
    { code: '111', name: 'Tiền mặt', type: 'ASSET', level: 1 },
    { code: '112', name: 'Tiền gửi ngân hàng', type: 'ASSET', level: 1 },
    { code: '131', name: '<PERSON><PERSON><PERSON> thu của khách hàng', type: 'ASSET', level: 1 },
    { code: '1311', name: '<PERSON><PERSON><PERSON> thu khách hàng trong nước', type: 'ASSET', parentCode: '131', level: 2 },
    { code: '1312', name: '<PERSON><PERSON><PERSON> thu khách hàng nước ngoài', type: 'ASSET', parentCode: '131', level: 2 },
    { code: '141', name: '<PERSON>ạ<PERSON> ứng', type: 'ASSET', level: 1 },
    { code: '152', name: '<PERSON><PERSON><PERSON> hóa', type: 'ASSET', level: 1 },
    { code: '153', name: 'Thành phẩm', type: 'ASSET', level: 1 },
    { code: '211', name: 'Tài sản cố định hữu hình', type: 'ASSET', level: 1 },
    { code: '2111', name: 'Nhà cửa, vật kiến trúc', type: 'ASSET', parentCode: '211', level: 2 },
    { code: '2112', name: 'Máy móc, thiết bị', type: 'ASSET', parentCode: '211', level: 2 },
    { code: '2113', name: 'Phương tiện vận tải', type: 'ASSET', parentCode: '211', level: 2 },
    { code: '1331', name: 'Thuế GTGT được khấu trừ', type: 'ASSET', level: 1 },

    // LIABILITIES (Nợ phải trả)
    { code: '331', name: 'Phải trả người bán', type: 'LIABILITY', level: 1 },
    { code: '3311', name: 'Phải trả người bán trong nước', type: 'LIABILITY', parentCode: '331', level: 2 },
    { code: '3312', name: 'Phải trả người bán nước ngoài', type: 'LIABILITY', parentCode: '331', level: 2 },
    { code: '334', name: 'Phải trả người lao động', type: 'LIABILITY', level: 1 },
    { code: '3341', name: 'Phải trả lương', type: 'LIABILITY', parentCode: '334', level: 2 },
    { code: '3348', name: 'Phải trả khác', type: 'LIABILITY', parentCode: '334', level: 2 },
    { code: '3331', name: 'Thuế GTGT phải nộp', type: 'LIABILITY', level: 1 },
    { code: '3332', name: 'Thuế GTGT phải thu hồi', type: 'LIABILITY', level: 1 },
    { code: '3335', name: 'Thuế thu nhập cá nhân', type: 'LIABILITY', level: 1 },
    { code: '3336', name: 'Thuế môn bài', type: 'LIABILITY', level: 1 },
    { code: '3338', name: 'Thuế tài nguyên, phí, lệ phí khác', type: 'LIABILITY', level: 1 },

    // EQUITY (Vốn chủ sở hữu)
    { code: '411', name: 'Vốn góp của chủ sở hữu', type: 'EQUITY', level: 1 },
    { code: '4111', name: 'Vốn góp bằng tiền', type: 'EQUITY', parentCode: '411', level: 2 },
    { code: '4112', name: 'Vốn góp bằng hiện vật', type: 'EQUITY', parentCode: '411', level: 2 },
    { code: '421', name: 'Lợi nhuận sau thuế chưa phân phối', type: 'EQUITY', level: 1 },

    // REVENUE (Doanh thu)
    { code: '511', name: 'Doanh thu bán hàng và cung cấp dịch vụ', type: 'REVENUE', level: 1 },
    { code: '5111', name: 'Doanh thu bán hàng hóa', type: 'REVENUE', parentCode: '511', level: 2 },
    { code: '5112', name: 'Doanh thu cung cấp dịch vụ', type: 'REVENUE', parentCode: '511', level: 2 },
    { code: '515', name: 'Doanh thu hoạt động tài chính', type: 'REVENUE', level: 1 },
    { code: '711', name: 'Thu nhập khác', type: 'REVENUE', level: 1 },

    // EXPENSES (Chi phí)
    { code: '621', name: 'Chi phí nguyên liệu, vật liệu trực tiếp', type: 'EXPENSE', level: 1 },
    { code: '622', name: 'Chi phí nhân công trực tiếp', type: 'EXPENSE', level: 1 },
    { code: '627', name: 'Chi phí sản xuất chung', type: 'EXPENSE', level: 1 },
    { code: '641', name: 'Chi phí bán hàng', type: 'EXPENSE', level: 1 },
    { code: '642', name: 'Chi phí quản lý doanh nghiệp', type: 'EXPENSE', level: 1 },
    { code: '811', name: 'Chi phí khác', type: 'EXPENSE', level: 1 },
    { code: '8211', name: 'Thuế thu nhập doanh nghiệp', type: 'EXPENSE', level: 1 }
  ];

  for (const account of accounts) {
    await prisma.account.upsert({
      where: { code: account.code },
      update: {},
      create: account
    });
  }

  console.log(`✅ Created ${accounts.length} accounts`);

  // Seed Tax Configuration
  console.log('💰 Seeding Tax Configuration...');
  
  const taxConfigs = [
    // VAT rates
    {
      taxType: 'VAT',
      rate: 0.00,
      description: 'Thuế GTGT 0% - Hàng xuất khẩu, dịch vụ giáo dục, y tế',
      applicableAccounts: ['5111', '5112'],
      effectiveFrom: new Date('2020-01-01')
    },
    {
      taxType: 'VAT',
      rate: 0.05,
      description: 'Thuế GTGT 5% - Nước sạch, một số dịch vụ',
      applicableAccounts: ['5112'],
      effectiveFrom: new Date('2020-01-01')
    },
    {
      taxType: 'VAT',
      rate: 0.10,
      description: 'Thuế GTGT 10% - Hàng hóa và dịch vụ thông thường',
      applicableAccounts: ['5111', '5112'],
      effectiveFrom: new Date('2020-01-01')
    },
    // Corporate Income Tax
    {
      taxType: 'CIT',
      rate: 0.20,
      description: 'Thuế TNDN 20% - Mức thuế suất chung',
      applicableAccounts: ['511', '515', '711'],
      effectiveFrom: new Date('2020-01-01')
    },
    {
      taxType: 'CIT',
      rate: 0.10,
      description: 'Thuế TNDN 10% - Doanh nghiệp nhỏ và vừa',
      applicableAccounts: ['511', '515', '711'],
      effectiveFrom: new Date('2020-01-01')
    },
    // License Tax
    {
      taxType: 'LICENSE',
      rate: 0.00,
      description: 'Thuế môn bài - Tính theo quy mô doanh nghiệp',
      applicableAccounts: [],
      effectiveFrom: new Date('2020-01-01')
    }
  ];

  for (const taxConfig of taxConfigs) {
    await prisma.taxConfig.upsert({
      where: {
        taxType_rate_effectiveFrom: {
          taxType: taxConfig.taxType,
          rate: taxConfig.rate,
          effectiveFrom: taxConfig.effectiveFrom
        }
      },
      update: {},
      create: taxConfig
    });
  }

  console.log(`✅ Created ${taxConfigs.length} tax configurations`);

  console.log('🎉 Database seeding completed successfully!');
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
