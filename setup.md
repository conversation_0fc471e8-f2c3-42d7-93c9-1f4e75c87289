# Hướng dẫn cài đặt và chạy ứng dụng

## Yêu cầu hệ thống

- **Node.js**: >= 16.0.0
- **PostgreSQL**: >= 13.0
- **npm** hoặc **yarn**

## Cài đặt

### 1. Cài đặt dependencies

```bash
# Cài đặt dependencies cho tất cả modules
npm run install:all

# Hoặc cài đặt từng module riêng biệt
npm install
cd client && npm install
cd ../server && npm install
```

### 2. Thiết lập database

```bash
# Tạo database PostgreSQL
createdb app_ke_toan_133

# Hoặc sử dụng psql
psql -U postgres
CREATE DATABASE app_ke_toan_133;
\q
```

### 3. Cấu hình environment variables

```bash
# Copy file .env.example trong thư mục server
cd server
cp .env.example .env

# Chỉnh sửa file .env với thông tin database và các cấu hình khác
nano .env
```

**C<PERSON><PERSON> hình cần thiết trong .env:**

```env
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/app_ke_toan_133"

# Server
PORT=5000
NODE_ENV=development

# JWT
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=7d

# Google APIs (tùy chọn)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_REDIRECT_URI=http://localhost:5000/auth/google/callback

# Email (tùy chọn)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
```

### 4. Chạy database migrations và seed data

```bash
cd server

# Generate Prisma client
npm run db:generate

# Chạy migrations
npm run db:migrate

# Seed initial data (Chart of Accounts, Tax Config)
npm run db:seed
```

### 5. Chạy ứng dụng

#### Development mode (khuyến nghị)

```bash
# Chạy cả frontend và backend cùng lúc
npm run dev
```

#### Chạy riêng biệt

```bash
# Terminal 1: Chạy backend
cd server
npm run dev

# Terminal 2: Chạy frontend
cd client
npm start
```

## Truy cập ứng dụng

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:5000
- **API Documentation**: http://localhost:5000/api-docs
- **Database Studio**: `npm run db:studio` (trong thư mục server)

## Tài khoản demo

Sau khi chạy ứng dụng, bạn có thể:

1. **Đăng ký tài khoản mới** tại http://localhost:3000/register
2. **Đăng nhập** tại http://localhost:3000/login

## Cấu trúc dự án

```
app_ke_toan_133/
├── client/                 # React frontend
│   ├── src/
│   │   ├── components/     # Reusable components
│   │   ├── pages/         # Page components
│   │   ├── contexts/      # React contexts
│   │   ├── services/      # API services
│   │   └── ...
│   └── package.json
├── server/                # Node.js backend
│   ├── src/
│   │   ├── routes/        # API routes
│   │   ├── middleware/    # Express middleware
│   │   └── ...
│   ├── prisma/           # Database schema & migrations
│   └── package.json
├── docs/                 # Documentation
└── package.json         # Root package.json
```

## Tính năng hiện tại

### ✅ Đã hoàn thành

- **Authentication**: Đăng ký, đăng nhập, quản lý session
- **Database Schema**: Thiết kế database theo TT 133/2016
- **API Backend**: RESTful API với đầy đủ endpoints
- **Frontend Layout**: Giao diện responsive với Material-UI
- **Dashboard**: Tổng quan tài chính cơ bản

### 🔄 Đang phát triển

- **Transaction Management**: CRUD giao dịch với wizard
- **Category Management**: Quản lý danh mục thu/chi
- **Tax Calculation**: Tính toán thuế tự động
- **Reports**: Báo cáo tài chính theo TT 133
- **Google Sheets Integration**: Đồng bộ dữ liệu

### ⏳ Sắp triển khai

- **Advanced Reports**: Báo cáo thuế chi tiết
- **Notifications**: Nhắc nhở nghĩa vụ thuế
- **Data Export**: Xuất báo cáo PDF, Excel
- **Mobile Optimization**: Tối ưu cho mobile

## Troubleshooting

### Lỗi database connection

```bash
# Kiểm tra PostgreSQL đang chạy
sudo service postgresql status

# Khởi động PostgreSQL nếu cần
sudo service postgresql start

# Kiểm tra connection string trong .env
```

### Lỗi port đã được sử dụng

```bash
# Kiểm tra process đang sử dụng port
lsof -i :3000  # Frontend
lsof -i :5000  # Backend

# Kill process nếu cần
kill -9 <PID>
```

### Lỗi Prisma

```bash
# Reset database (cẩn thận - sẽ xóa tất cả data)
cd server
npx prisma migrate reset

# Regenerate Prisma client
npx prisma generate
```

## Hỗ trợ

- **Documentation**: Xem thư mục `docs/`
- **API Docs**: http://localhost:5000/api-docs
- **Issues**: Tạo issue trên GitHub repository

## License

MIT License - xem file LICENSE để biết thêm chi tiết.
