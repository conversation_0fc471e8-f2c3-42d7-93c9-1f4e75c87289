<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>App Kế toán TT 133 - Phiên bản Standalone</title>

    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            width: 95%;
            max-width: 1200px;
            min-height: 80vh;
            display: flex;
        }

        .sidebar {
            background: #2c3e50;
            color: white;
            width: 280px;
            padding: 2rem;
            display: flex;
            flex-direction: column;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .logo-icon {
            width: 40px;
            height: 40px;
            background: #3498db;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .nav-menu {
            list-style: none;
        }

        .nav-item {
            margin-bottom: 0.5rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            border-radius: 8px;
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            transition: all 0.3s;
            cursor: pointer;
        }

        .nav-link:hover, .nav-link.active {
            background: rgba(255,255,255,0.1);
            color: white;
        }

        .main-content {
            flex: 1;
            padding: 2rem;
            background: #f8f9fa;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid;
        }

        .stat-card.income { border-left-color: #27ae60; }
        .stat-card.expense { border-left-color: #e74c3c; }
        .stat-card.profit { border-left-color: #3498db; }
        .stat-card.transactions { border-left-color: #f39c12; }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }

        .content-section {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
        }

        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: background 0.3s;
        }

        .btn:hover {
            background: #2980b9;
        }

        .btn-success {
            background: #27ae60;
        }

        .btn-success:hover {
            background: #229954;
        }

        .transaction-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #333;
        }

        .form-group input, .form-group select {
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 1rem;
        }

        .transaction-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .transaction-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid #eee;
        }

        .transaction-item:last-child {
            border-bottom: none;
        }

        .amount-positive {
            color: #27ae60;
            font-weight: 600;
        }

        .amount-negative {
            color: #e74c3c;
            font-weight: 600;
        }

        .hidden {
            display: none;
        }

        @media (max-width: 768px) {
            .container {
                flex-direction: column;
                width: 100%;
                height: 100vh;
                border-radius: 0;
            }

            .sidebar {
                width: 100%;
                padding: 1rem;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="logo">
                <div class="logo-icon">
                    <span class="material-icons">trending_up</span>
                </div>
                <div>
                    <h3>Kế toán TT 133</h3>
                    <small>Phiên bản Standalone</small>
                </div>
            </div>

            <nav>
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="#" class="nav-link active" onclick="showSection('dashboard')">
                            <span class="material-icons">dashboard</span>
                            Tổng quan
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showSection('transactions')">
                            <span class="material-icons">receipt</span>
                            Giao dịch
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showSection('reports')">
                            <span class="material-icons">assessment</span>
                            Báo cáo
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showSection('tax')">
                            <span class="material-icons">account_balance</span>
                            Quản lý thuế
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showSection('google-sheets')">
                            <span class="material-icons">cloud_sync</span>
                            Google Sheets
                        </a>
                    </li>
                </ul>
            </nav>

            <div style="margin-top: auto; padding-top: 2rem; border-top: 1px solid rgba(255,255,255,0.1);">
                <small>© 2024 App Kế toán TT 133</small>
                <br>
                <small style="opacity: 0.7;">Phiên bản demo - Dữ liệu lưu trên trình duyệt</small>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Dashboard Section -->
            <div id="dashboard" class="section">
                <div class="header">
                    <h1>Dashboard</h1>
                    <button class="btn" onclick="showSection('transactions')">
                        <span class="material-icons" style="vertical-align: middle; margin-right: 0.5rem;">add</span>
                        Thêm giao dịch
                    </button>
                </div>

                <div class="stats-grid">
                    <div class="stat-card income">
                        <div class="stat-value" id="totalIncome">0 ₫</div>
                        <div class="stat-label">Tổng thu tháng này</div>
                    </div>
                    <div class="stat-card expense">
                        <div class="stat-value" id="totalExpense">0 ₫</div>
                        <div class="stat-label">Tổng chi tháng này</div>
                    </div>
                    <div class="stat-card profit">
                        <div class="stat-value" id="netProfit">0 ₫</div>
                        <div class="stat-label">Lợi nhuận ròng</div>
                    </div>
                    <div class="stat-card transactions">
                        <div class="stat-value" id="transactionCount">0</div>
                        <div class="stat-label">Số giao dịch</div>
                    </div>
                </div>

                <div class="content-section">
                    <h3>Giao dịch gần đây</h3>
                    <div id="recentTransactions" class="transaction-list">
                        <p style="text-align: center; color: #666; padding: 2rem;">
                            Chưa có giao dịch nào. Hãy thêm giao dịch đầu tiên!
                        </p>
                    </div>
                </div>
            </div>

            <!-- Transactions Section -->
            <div id="transactions" class="section hidden">
                <div class="header">
                    <h1>Quản lý giao dịch</h1>
                </div>

                <div class="content-section">
                    <h3>Thêm giao dịch mới</h3>
                    <form id="transactionForm" class="transaction-form">
                        <div class="form-group">
                            <label>Loại giao dịch</label>
                            <select id="transactionType" required>
                                <option value="">Chọn loại</option>
                                <option value="income">Thu nhập</option>
                                <option value="expense">Chi phí</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Mô tả</label>
                            <input type="text" id="description" placeholder="Mô tả giao dịch" required>
                        </div>
                        <div class="form-group">
                            <label>Số tiền (VND)</label>
                            <input type="number" id="amount" placeholder="0" min="0" required>
                        </div>
                        <div class="form-group">
                            <label>Ngày</label>
                            <input type="date" id="date" required>
                        </div>
                        <div class="form-group">
                            <label>Danh mục</label>
                            <select id="category" required>
                                <option value="">Chọn danh mục</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Ghi chú</label>
                            <input type="text" id="notes" placeholder="Ghi chú (tùy chọn)">
                        </div>
                    </form>
                    <button type="submit" form="transactionForm" class="btn btn-success">
                        <span class="material-icons" style="vertical-align: middle; margin-right: 0.5rem;">save</span>
                        Lưu giao dịch
                    </button>
                </div>

                <div class="content-section">
                    <h3>Danh sách giao dịch</h3>
                    <div id="allTransactions" class="transaction-list">
                        <p style="text-align: center; color: #666; padding: 2rem;">
                            Chưa có giao dịch nào
                        </p>
                    </div>
                </div>
            </div>

            <!-- Reports Section -->
            <div id="reports" class="section hidden">
                <div class="header">
                    <h1>Báo cáo tài chính</h1>
                </div>

                <div class="content-section">
                    <h3>Báo cáo tháng này</h3>
                    <div id="monthlyReport">
                        <p>Báo cáo sẽ được tạo tự động dựa trên dữ liệu giao dịch</p>
                    </div>
                </div>
            </div>

            <!-- Tax Section -->
            <div id="tax" class="section hidden">
                <div class="header">
                    <h1>Quản lý thuế</h1>
                </div>

                <div class="content-section">
                    <h3>Tính toán thuế GTGT</h3>
                    <div class="transaction-form">
                        <div class="form-group">
                            <label>Doanh thu (chưa thuế)</label>
                            <input type="number" id="revenue" placeholder="0" min="0">
                        </div>
                        <div class="form-group">
                            <label>Thuế suất GTGT</label>
                            <select id="vatRate">
                                <option value="0">0% (Xuất khẩu, Y tế, Giáo dục)</option>
                                <option value="5">5% (Nước sạch, một số dịch vụ)</option>
                                <option value="10" selected>10% (Hàng hóa, dịch vụ thông thường)</option>
                            </select>
                        </div>
                    </div>
                    <button class="btn" onclick="calculateVAT()">Tính thuế GTGT</button>
                    <div id="vatResult" style="margin-top: 1rem;"></div>
                </div>
            </div>

            <!-- Google Sheets Section -->
            <div id="google-sheets" class="section hidden">
                <div class="header">
                    <h1>Đồng bộ Google Sheets</h1>
                </div>

                <div class="content-section">
                    <div id="sheets-disconnected" class="sheets-status">
                        <h3>Kết nối Google Sheets</h3>
                        <p style="margin-bottom: 1rem; color: #666;">
                            Kết nối với Google Sheets để tự động đồng bộ dữ liệu kế toán của bạn lên cloud.
                            Dữ liệu sẽ được tổ chức theo các sheet: Thu nhập, Chi phí, và Tổng hợp.
                        </p>
                        <button class="btn btn-success" onclick="connectGoogleSheets()">
                            <span class="material-icons" style="vertical-align: middle; margin-right: 0.5rem;">cloud_upload</span>
                            Kết nối Google Sheets
                        </button>
                    </div>

                    <div id="sheets-connected" class="sheets-status hidden">
                        <h3>✅ Đã kết nối Google Sheets</h3>
                        <p style="margin-bottom: 1rem; color: #27ae60;">
                            Dữ liệu của bạn đang được đồng bộ với Google Sheets.
                        </p>

                        <div style="display: flex; gap: 1rem; margin-bottom: 1rem; flex-wrap: wrap;">
                            <button class="btn" onclick="syncToSheets()">
                                <span class="material-icons" style="vertical-align: middle; margin-right: 0.5rem;">sync</span>
                                Đồng bộ ngay
                            </button>
                            <button class="btn" onclick="openGoogleSheet()" style="background: #34a853;">
                                <span class="material-icons" style="vertical-align: middle; margin-right: 0.5rem;">open_in_new</span>
                                Mở Google Sheet
                            </button>
                            <button class="btn" onclick="disconnectGoogleSheets()" style="background: #dc3545;">
                                <span class="material-icons" style="vertical-align: middle; margin-right: 0.5rem;">cloud_off</span>
                                Ngắt kết nối
                            </button>
                        </div>

                        <div id="sync-status" style="padding: 1rem; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #17a2b8;">
                            <strong>Trạng thái đồng bộ:</strong>
                            <div id="sync-info">Chưa đồng bộ lần nào</div>
                        </div>
                    </div>
                </div>

                <div class="content-section">
                    <h3>Cấu hình đồng bộ</h3>
                    <div class="transaction-form">
                        <div class="form-group">
                            <label>Tự động đồng bộ</label>
                            <select id="autoSync">
                                <option value="manual">Thủ công</option>
                                <option value="immediate">Ngay lập tức khi có giao dịch mới</option>
                                <option value="daily">Hàng ngày</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Tên Google Sheet</label>
                            <input type="text" id="sheetName" placeholder="Kế toán TT133" value="Kế toán TT133">
                        </div>
                    </div>
                    <button class="btn" onclick="updateSyncSettings()">Cập nhật cài đặt</button>
                </div>

                <div class="content-section">
                    <h3>Hướng dẫn sử dụng</h3>
                    <div style="background: #e3f2fd; padding: 1rem; border-radius: 8px; border-left: 4px solid #2196f3;">
                        <h4>📋 Cách thức hoạt động:</h4>
                        <ol style="margin: 0.5rem 0; padding-left: 1.5rem;">
                            <li>Nhấn "Kết nối Google Sheets" để đăng nhập Google</li>
                            <li>Ứng dụng sẽ tạo một Google Sheet mới với 3 tab:
                                <ul style="margin: 0.5rem 0; padding-left: 1rem;">
                                    <li><strong>Thu nhập:</strong> Tất cả giao dịch thu</li>
                                    <li><strong>Chi phí:</strong> Tất cả giao dịch chi</li>
                                    <li><strong>Tổng hợp:</strong> Báo cáo tổng hợp theo tháng</li>
                                </ul>
                            </li>
                            <li>Dữ liệu sẽ được đồng bộ theo cài đặt của bạn</li>
                            <li>Bạn có thể mở Google Sheet để xem và chỉnh sửa</li>
                        </ol>

                        <h4 style="margin-top: 1rem;">🔒 Bảo mật:</h4>
                        <ul style="margin: 0.5rem 0; padding-left: 1.5rem;">
                            <li>Chỉ bạn mới có quyền truy cập Google Sheet</li>
                            <li>Ứng dụng chỉ đọc/ghi dữ liệu, không truy cập thông tin khác</li>
                            <li>Bạn có thể ngắt kết nối bất cứ lúc nào</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Google APIs -->
    <script src="https://apis.google.com/js/api.js"></script>
    <script src="standalone-app.js"></script>
</body>
</html>
